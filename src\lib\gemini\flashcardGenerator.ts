import { prepararDocumentos } from './geminiClient';
import { PROMPT_FLASHCARDS } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';
import { type Chunk } from '@/lib/utils/textProcessing';
import { seleccionarChunksRelevantes } from '@/lib/utils/chunkSelector';
import * as Sentry from "@sentry/nextjs";

/**
 * Genera flashcards a partir de los documentos
 */
export async function generarFlashcards(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  cantidad: number = 10,
  instrucciones?: string
): Promise<{ pregunta: string; respuesta: string }[]> {
  try {
    // Preparar el contenido de los documentos
    const resultadoDocumentos = prepararDocumentos(documentos);

    // Verificar si se usó chunking
    if (resultadoDocumentos.wasChunked && Array.isArray(resultadoDocumentos.content)) {
      return await procesarFlashcardsConChunks(resultadoDocumentos.content, cantidad, instrucciones, documentos);
    } else {
      // Procesamiento tradicional para .
      // sin chunking
      const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
        ? resultadoDocumentos.content.join('\n\n')
        : resultadoDocumentos.content;

      if (!contenidoDocumentos) {
        throw new Error("No se han proporcionado documentos para generar flashcards.");
      }

      return await procesarFlashcardsSinChunks(contenidoDocumentos, cantidad, instrucciones);
    }


  } catch (error) {
    console.error('Error al generar flashcards:', error);
    throw error;
  }
}

/**
 * Procesa flashcards con chunking - selecciona chunks relevantes y los combina en un solo contexto
 */
async function procesarFlashcardsConChunks(
  chunks: Chunk[],
  cantidad: number,
  instrucciones?: string,
  documentos?: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<{ pregunta: string; respuesta: string }[]> {
  // --- INICIO DE LA NUEVA LÓGICA DE SELECCIÓN ---
  const selectionResult = seleccionarChunksRelevantes(chunks, instrucciones || '');

  if (selectionResult.selectedChunks.length === 0) {
    throw new Error("No se pudo encontrar contenido relevante para tu petición. Intenta ser más específico.");
  }

  // --- FIN DE LA NUEVA LÓGICA DE SELECCIÓN ---

  // PASO 2 (NUEVO): Combinar el contenido de los chunks seleccionados en un solo contexto
  const combinedContent = selectionResult.selectedChunks
    .map(chunk => chunk.content)
    .join('\n\n---\n\n'); // Un separador claro ayuda a la IA

  // PASO 3: Preparar el prompt para una ÚNICA llamada
  let prompt = PROMPT_FLASHCARDS
    .replace('{documentos}', combinedContent) // Usar el contenido combinado
    .replace('{cantidad}', cantidad.toString());

  if (instrucciones) {
    prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
  } else {
    prompt = prompt.replace('{instrucciones}', '');
  }

  // PASO 4: Hacer UNA SOLA llamada a la IA
  const config = getOpenAIConfig('FLASHCARDS');
  const messages = [{ role: 'user' as const, content: prompt }];
  const responseText = await llamarOpenAI(messages, {
    ...config,
    activityName: `Generación [Combinada] de Flashcards (${cantidad} tarjetas)`
  });

  // PASO 5: Parsear la respuesta directamente (ya no se necesita combinar)
  const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);
  if (!jsonMatch) {
    throw new Error("La IA no devolvió un formato JSON válido.");
  }

  const flashcardsJson = jsonMatch[0];
  const flashcards = JSON.parse(flashcardsJson);

  // Validar formato básico
  if (!Array.isArray(flashcards) || flashcards.length === 0) {
    throw new Error("La IA no generó flashcards válidas.");
  }

  // Validar estructura de cada flashcard
  const flashcardsValidas = flashcards.filter(flashcard =>
    flashcard.pregunta &&
    flashcard.respuesta
  );

  if (flashcardsValidas.length === 0) {
    throw new Error("Ninguna de las flashcards generadas tiene el formato correcto.");
  }

  return flashcardsValidas;
}

/**
 * Procesa flashcards sin chunking - método tradicional
 */
async function procesarFlashcardsSinChunks(
  contenidoDocumentos: string,
  cantidad: number,
  instrucciones?: string
): Promise<{ pregunta: string; respuesta: string }[]> {
  // Construir el prompt para la IA usando el prompt personalizado
  let prompt = PROMPT_FLASHCARDS
    .replace('{documentos}', contenidoDocumentos)
    .replace('{cantidad}', cantidad.toString());

  // Añadir instrucciones adicionales si se proporcionan
  if (instrucciones) {
    prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
  } else {
    prompt = prompt.replace('{instrucciones}', '');
  }

  // Obtener configuración específica para flashcards
  const config = getOpenAIConfig('FLASHCARDS');


  // Generar las flashcards usando OpenAI con la configuración correcta
  const messages = [{ role: 'user' as const, content: prompt }];
  const responseText = await llamarOpenAI(messages, {
    ...config,
    activityName: `Generación de Flashcards (${cantidad || 'N/A'} tarjetas)`
  });

  // Extraer el JSON de la respuesta
  const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);

  if (!jsonMatch) {
    throw new Error("No se pudo extraer el formato JSON de la respuesta.");
  }

  const flashcardsJson = jsonMatch[0];
  const flashcards = JSON.parse(flashcardsJson);

  // Validar el formato
  if (!Array.isArray(flashcards) || flashcards.length === 0) {
    throw new Error("El formato de las flashcards generadas no es válido.");
  }

  return flashcards;
}
