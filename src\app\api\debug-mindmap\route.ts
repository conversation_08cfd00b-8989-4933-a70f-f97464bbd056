import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { GenerarMapaMentalSchema } from '@/lib/zodSchemas';

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 [DEBUG] Iniciando debug de mapa mental...');
    
    const body = await request.json();
    console.log('🔍 [DEBUG] Body recibido:', JSON.stringify(body, null, 2));
    
    // 1. Verificar validación Zod
    try {
      const validatedData = GenerarMapaMentalSchema.parse(body);
      console.log('✅ [DEBUG] Validación Zod exitosa:', validatedData);
    } catch (zodError) {
      console.error('❌ [DEBUG] Error de validación Zod:', zodError);
      return NextResponse.json({ 
        error: 'Error de validación Zod', 
        details: zodError 
      }, { status: 400 });
    }
    
    // 2. Verificar autenticación
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      console.error('❌ [DEBUG] No hay header de autorización');
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }
    
    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      global: { headers: { Authorization: authHeader } }
    });
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      console.error('❌ [DEBUG] Error de autenticación:', authError);
      return NextResponse.json({ error: 'Usuario no autenticado' }, { status: 401 });
    }
    
    console.log('✅ [DEBUG] Usuario autenticado:', user.id);
    
    // 3. Verificar tareas existentes
    const { data: existingTasks, error: checkError } = await supabase
      .from('ai_tasks')
      .select('id, status')
      .eq('user_id', user.id)
      .eq('type', 'mindmap')
      .in('status', ['pending', 'processing']);
    
    if (checkError) {
      console.error('❌ [DEBUG] Error al verificar tareas:', checkError);
      return NextResponse.json({ error: 'Error al verificar tareas' }, { status: 500 });
    }
    
    console.log('🔍 [DEBUG] Tareas existentes:', existingTasks);
    
    if (existingTasks && existingTasks.length > 0) {
      console.log('⚠️ [DEBUG] Ya hay tareas en proceso');
      return NextResponse.json({
        error: 'Ya tienes una generación de mapa mental en proceso'
      }, { status: 409 });
    }
    
    // 4. Intentar crear la tarea
    const taskData = {
      user_id: user.id,
      type: 'mindmap',
      input_data: {
        peticion: body.peticion,
        documentos: body.documentos
      }
    };
    
    console.log('🔍 [DEBUG] Datos de la tarea a crear:', JSON.stringify(taskData, null, 2));
    
    const { data: newTask, error: taskError } = await supabase
      .from('ai_tasks')
      .insert(taskData)
      .select()
      .single();
    
    if (taskError) {
      console.error('❌ [DEBUG] Error al crear tarea:', taskError);
      return NextResponse.json({ 
        error: 'Error al crear la tarea', 
        details: taskError 
      }, { status: 500 });
    }
    
    console.log('✅ [DEBUG] Tarea creada exitosamente:', newTask);
    
    return NextResponse.json({ 
      success: true, 
      taskId: newTask.id,
      debug: {
        user: user.id,
        existingTasks: existingTasks?.length || 0,
        taskData
      }
    });
    
  } catch (error) {
    console.error('❌ [DEBUG] Error general:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor', 
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}
