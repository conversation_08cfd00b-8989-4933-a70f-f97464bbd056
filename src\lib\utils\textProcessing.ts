/**
 * Utilidades de Procesamiento de Texto para Chunking Inteligente
 * 
 * Este módulo proporciona funciones para dividir documentos largos en chunks
 * manteniendo la coherencia del contenido y respetando la estructura de apartados.
 * 
 * Patrones de apartados soportados:
 * - "1.- Título"
 * - "1.1.- Subtítulo" 
 * - "APARTADO 1:"
 * - "TEMA 1:"
 * - Y variaciones similares
 */

export interface ChunkingConfig {
  maxChunkSize: number;
  minChunkSize?: number;
  overlapSize: number;
  sectionPatterns: RegExp[];
  fallbackToSentences: boolean;
}

export interface ChunkMetadata {
  chunkNumber: number;
  totalChunks: number;
  sourceDocumentId?: string;
  hasOverlap: boolean;
  detectedSections: string[];
  chunkType: 'section' | 'paragraph' | 'sentence' | 'fixed';
}

export interface Chunk {
  content: string;
  metadata: ChunkMetadata;
}

/**
 * Patrones de expresiones regulares para detectar apartados y secciones
 */
export const DEFAULT_SECTION_PATTERNS: RegExp[] = [
  // Patrones principales de apartados
  /^\d+\.-\s*.+$/gm,                    // "1.- Título"
  /^\d+\.\d+\.-\s*.+$/gm,               // "1.1.- Subtítulo"
  /^\d+\.\d+\.\d+\.-\s*.+$/gm,          // "1.1.1.- Sub-subtítulo"
  
  // Patrones alternativos
  /^APARTADO\s+\d+[:\.-]\s*.+$/gmi,     // "APARTADO 1: Título"
  /^TEMA\s+\d+[:\.-]\s*.+$/gmi,         // "TEMA 1: Título"
  /^CAPÍTULO\s+\d+[:\.-]\s*.+$/gmi,     // "CAPÍTULO 1: Título"
  /^SECCIÓN\s+\d+[:\.-]\s*.+$/gmi,      // "SECCIÓN 1: Título"
  
  // Patrones con números romanos
  /^[IVX]+\.-\s*.+$/gm,                 // "I.- Título"
  /^[IVX]+\.\s*.+$/gm,                  // "I. Título"
  
  // Patrones con letras
  /^[A-Z]\)\s*.+$/gm,                   // "A) Título"
  /^[a-z]\)\s*.+$/gm,                   // "a) Título"
];

/**
 * Configuración por defecto para el chunking
 */
export const DEFAULT_CHUNKING_CONFIG: ChunkingConfig = {
  maxChunkSize: 40000,
  minChunkSize: 8000,
  overlapSize: 2000,
  sectionPatterns: DEFAULT_SECTION_PATTERNS,
  fallbackToSentences: true,
};

/**
 * Detecta todas las secciones/apartados en un texto
 */
export function detectarSecciones(content: string, patterns: RegExp[] = DEFAULT_SECTION_PATTERNS): Array<{
  title: string;
  startIndex: number;
  pattern: string;
}> {
  const secciones: Array<{ title: string; startIndex: number; pattern: string }> = [];
  
  patterns.forEach((pattern, patternIndex) => {
    const matches = Array.from(content.matchAll(pattern));
    matches.forEach(match => {
      if (match.index !== undefined) {
        secciones.push({
          title: match[0].trim(),
          startIndex: match.index,
          pattern: `pattern_${patternIndex}`,
        });
      }
    });
  });
  
  // Ordenar por posición en el texto
  return secciones.sort((a, b) => a.startIndex - b.startIndex);
}

/**
 * Interfaz para secciones jerarquizadas
 */
interface SeccionJerarquizada {
  nivel: 1 | 2 | 3;
  titulo: string;
  startIndex: number;
  endIndex: number;
  tamaño: number;
  pattern: string;
}

/**
 * Clasifica secciones por jerarquía de importancia
 */
function clasificarSecciones(
  secciones: Array<{ title: string; startIndex: number; pattern: string }>,
  content: string
): SeccionJerarquizada[] {
  return secciones.map((seccion, index) => {
    const siguienteSeccion = secciones[index + 1];
    const endIndex = siguienteSeccion ? siguienteSeccion.startIndex : content.length;
    const tamaño = endIndex - seccion.startIndex;

    // Determinar nivel basado en el patrón
    let nivel: 1 | 2 | 3 = 1;

    // Nivel 1: Títulos principales
    if (seccion.title.match(/^(TEMA|CAPÍTULO|APARTADO|SECCIÓN)\s+\d+/i) ||
        seccion.title.match(/^\d+\.-\s/)) {
      nivel = 1;
    }
    // Nivel 2: Subsecciones
    else if (seccion.title.match(/^\d+\.\d+\.-\s/)) {
      nivel = 2;
    }
    // Nivel 3: Sub-subsecciones
    else if (seccion.title.match(/^\d+\.\d+\.\d+\.-\s/)) {
      nivel = 3;
    }

    return {
      nivel,
      titulo: seccion.title,
      startIndex: seccion.startIndex,
      endIndex,
      tamaño,
      pattern: seccion.pattern
    };
  });
}

/**
 * Divide un texto en chunks basándose en secciones detectadas con agrupación inteligente
 */
export function dividirPorSecciones(
  content: string,
  secciones: Array<{ title: string; startIndex: number; pattern: string }>,
  config: ChunkingConfig
): string[] {
  if (secciones.length === 0) {
    return [content];
  }

  // Clasificar secciones por jerarquía
  const seccionesJerarquizadas = clasificarSecciones(secciones, content);

  // Configurar tamaño mínimo por chunk
  const minChunkSize = config.minChunkSize || Math.floor(config.maxChunkSize * 0.2);

  const chunks: string[] = [];
  let chunkActual = '';
  let inicioChunkActual = 0;

  for (let i = 0; i < seccionesJerarquizadas.length; i++) {
    const seccionActual = seccionesJerarquizadas[i];
    const contenidoSeccion = content.substring(seccionActual.startIndex, seccionActual.endIndex).trim();

    // Si es la primera sección del chunk actual
    if (chunkActual === '') {
      chunkActual = contenidoSeccion;
      inicioChunkActual = seccionActual.startIndex;
    } else {
      const chunkConNuevaSeccion = chunkActual + '\n\n' + contenidoSeccion;

      // Verificar si agregar esta sección excede el límite
      if (chunkConNuevaSeccion.length > config.maxChunkSize) {
        // Si el chunk actual es muy pequeño, intentar incluir la sección actual
        if (chunkActual.length < minChunkSize && seccionActual.nivel >= 2) {
          // Para subsecciones, intentar incluir si no excede demasiado
          if (chunkConNuevaSeccion.length <= config.maxChunkSize * 1.1) {
            chunkActual = chunkConNuevaSeccion;
          } else {
            // Finalizar chunk actual y empezar nuevo
            if (chunkActual.length > 0) {
              chunks.push(chunkActual);
            }
            chunkActual = contenidoSeccion;
            inicioChunkActual = seccionActual.startIndex;
          }
        } else {
          // Finalizar chunk actual y empezar nuevo
          if (chunkActual.length > 0) {
            chunks.push(chunkActual);
          }
          chunkActual = contenidoSeccion;
          inicioChunkActual = seccionActual.startIndex;
        }
      } else {
        // La sección cabe en el chunk actual
        chunkActual = chunkConNuevaSeccion;
      }
    }

    // Si es la última sección o la siguiente es de nivel 1, considerar finalizar chunk
    const siguienteSeccion = seccionesJerarquizadas[i + 1];
    const deberiaFinalizarChunk = !siguienteSeccion ||
      (siguienteSeccion.nivel === 1 && chunkActual.length >= minChunkSize);

    if (deberiaFinalizarChunk && chunkActual.length > 0) {
      // Si el chunk es demasiado grande, subdividirlo
      if (chunkActual.length > config.maxChunkSize) {
        const subChunks = subdividirChunkGrande(chunkActual, config);
        chunks.push(...subChunks);
      } else {
        chunks.push(chunkActual);
      }
      chunkActual = '';
    }
  }

  // Manejar chunk final si queda contenido
  if (chunkActual.length > 0) {
    if (chunkActual.length > config.maxChunkSize) {
      const subChunks = subdividirChunkGrande(chunkActual, config);
      chunks.push(...subChunks);
    } else {
      chunks.push(chunkActual);
    }
  }

  return chunks;
}

/**
 * Subdivide un chunk que es demasiado grande
 */
export function subdividirChunkGrande(content: string, config: ChunkingConfig): string[] {
  const chunks: string[] = [];
  
  // Intentar dividir por párrafos primero
  const parrafos = content.split(/\n\s*\n/);
  let chunkActual = '';
  
  for (const parrafo of parrafos) {
    const parrafoConSalto = parrafo.trim() + '\n\n';
    
    if ((chunkActual + parrafoConSalto).length > config.maxChunkSize) {
      if (chunkActual.length > 0) {
        chunks.push(chunkActual.trim());
        chunkActual = '';
      }
      
      // Si un párrafo individual es muy grande, dividirlo por oraciones
      if (parrafoConSalto.length > config.maxChunkSize) {
        const subChunks = dividirPorOraciones(parrafoConSalto, config.maxChunkSize);
        chunks.push(...subChunks);
      } else {
        chunkActual = parrafoConSalto;
      }
    } else {
      chunkActual += parrafoConSalto;
    }
  }
  
  if (chunkActual.trim().length > 0) {
    chunks.push(chunkActual.trim());
  }
  
  return chunks;
}

/**
 * Divide un texto por oraciones cuando los párrafos son demasiado grandes
 */
export function dividirPorOraciones(content: string, maxSize: number): string[] {
  const oraciones = content.split(/(?<=[.!?])\s+/);
  const chunks: string[] = [];
  let chunkActual = '';
  
  for (const oracion of oraciones) {
    if ((chunkActual + oracion).length > maxSize) {
      if (chunkActual.length > 0) {
        chunks.push(chunkActual.trim());
        chunkActual = oracion + ' ';
      } else {
        // Oracion individual muy larga, dividir por tamaño fijo
        chunks.push(...dividirPorTamanoFijo(oracion, maxSize));
      }
    } else {
      chunkActual += oracion + ' ';
    }
  }
  
  if (chunkActual.trim().length > 0) {
    chunks.push(chunkActual.trim());
  }
  
  return chunks;
}

/**
 * División por tamaño fijo como último recurso
 */
export function dividirPorTamanoFijo(content: string, maxSize: number): string[] {
  const chunks: string[] = [];
  
  for (let i = 0; i < content.length; i += maxSize) {
    chunks.push(content.substring(i, i + maxSize));
  }
  
  return chunks;
}

/**
 * Añade solapamiento entre chunks para mantener contexto
 */
export function añadirSolapamiento(chunks: string[], overlapSize: number): string[] {
  if (chunks.length <= 1 || overlapSize <= 0) {
    return chunks;
  }
  
  const chunksConSolapamiento: string[] = [];
  
  for (let i = 0; i < chunks.length; i++) {
    let chunkConSolapamiento = chunks[i];
    
    // Añadir contexto del chunk anterior (excepto para el primero)
    if (i > 0) {
      const chunkAnterior = chunks[i - 1];
      const contextoAnterior = chunkAnterior.slice(-overlapSize);
      chunkConSolapamiento = `[CONTEXTO PREVIO]\n${contextoAnterior}\n[FIN CONTEXTO]\n\n${chunkConSolapamiento}`;
    }
    
    chunksConSolapamiento.push(chunkConSolapamiento);
  }
  
  return chunksConSolapamiento;
}

/**
 * Función principal para dividir contenido en chunks inteligentes
 */
export function dividirEnChunks(
  content: string, 
  config: ChunkingConfig = DEFAULT_CHUNKING_CONFIG,
  documentId?: string
): Chunk[] {
  // Si el contenido es pequeño, no necesita chunking
  if (content.length <= config.maxChunkSize) {
    return [{
      content,
      metadata: {
        chunkNumber: 1,
        totalChunks: 1,
        sourceDocumentId: documentId,
        hasOverlap: false,
        detectedSections: [],
        chunkType: 'section'
      }
    }];
  }
  
  // Detectar secciones
  const secciones = detectarSecciones(content, config.sectionPatterns);
  
  let chunks: string[];
  let chunkType: ChunkMetadata['chunkType'] = 'section';
  
  if (secciones.length > 1) {
    // Dividir por secciones detectadas
    chunks = dividirPorSecciones(content, secciones, config);
  } else {
    // Fallback: dividir por párrafos o oraciones
    chunkType = 'paragraph';
    chunks = subdividirChunkGrande(content, config);
  }
  
  // Añadir solapamiento
  const chunksConSolapamiento = añadirSolapamiento(chunks, config.overlapSize);
  
  // Crear objetos Chunk con metadata
  return chunksConSolapamiento.map((chunk, index) => ({
    content: chunk,
    metadata: {
      chunkNumber: index + 1,
      totalChunks: chunksConSolapamiento.length,
      sourceDocumentId: documentId,
      hasOverlap: index > 0,
      detectedSections: secciones.map(s => s.title),
      chunkType
    }
  }));
}

/**
 * Función de utilidad para obtener estadísticas de chunking
 */
export function obtenerEstadisticasChunking(chunks: Chunk[]): {
  totalChunks: number;
  averageSize: number;
  minSize: number;
  maxSize: number;
  totalSize: number;
  sectionsDetected: number;
} {
  if (chunks.length === 0) {
    return {
      totalChunks: 0,
      averageSize: 0,
      minSize: 0,
      maxSize: 0,
      totalSize: 0,
      sectionsDetected: 0
    };
  }
  
  const sizes = chunks.map(chunk => chunk.content.length);
  const totalSize = sizes.reduce((sum, size) => sum + size, 0);
  
  return {
    totalChunks: chunks.length,
    averageSize: Math.round(totalSize / chunks.length),
    minSize: Math.min(...sizes),
    maxSize: Math.max(...sizes),
    totalSize,
    sectionsDetected: chunks[0]?.metadata.detectedSections.length || 0
  };
}
