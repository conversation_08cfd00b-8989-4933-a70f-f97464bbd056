chunkselector.ts

/**
 * Selector de Chunks Relevantes
 * 
 * Este módulo implementa la lógica de selección inteligente de chunks
 * para optimizar el procesamiento de documentos largos, enfocando
 * la generación de IA solo en el contenido más relevante.
 */

import { type Chunk } from './textProcessing';
import { CHUNK_SELECTOR_CONFIG } from '@/config/chunking';

/**
 * Configuración del selector de chunks
 */
export interface ChunkSelectorConfig {
  /** Número máximo de chunks a seleccionar */
  topN: number;
  /** Puntuación mínima para considerar un chunk relevante */
  relevanceThreshold: number;
  /** Peso para coincidencias en títulos de sección (vs contenido) */
  sectionTitleWeight: number;
  /** Peso para coincidencias en contenido */
  contentWeight: number;
}

/**
 * Configuración por defecto del selector
 * Utiliza valores centralizados de la configuración global
 */
export const DEFAULT_CHUNK_SELECTOR_CONFIG: ChunkSelectorConfig = {
  topN: CHUNK_SELECTOR_CONFIG.TOP_N_CHUNKS,
  relevanceThreshold: CHUNK_SELECTOR_CONFIG.RELEVANCE_THRESHOLD,
  sectionTitleWeight: CHUNK_SELECTOR_CONFIG.SECTION_TITLE_WEIGHT,
  contentWeight: CHUNK_SELECTOR_CONFIG.CONTENT_WEIGHT
};

/**
 * Resultado de la selección de chunks con información de puntuación
 */
export interface ChunkSelectionResult {
  /** Chunks seleccionados ordenados por relevancia */
  selectedChunks: Chunk[];
  /** Número total de chunks evaluados */
  totalChunks: number;
  /** Criterios de selección utilizados */
  selectionCriteria: {
    keywords: string[];
    maxChunks: number;
    minScore: number;
  };
  /** Información de puntuación para debugging */
  scoringInfo: {
    chunkIndex: number;
    score: number;
    matchedKeywords: string[];
    sectionMatches: number;
    contentMatches: number;
  }[];
}

/**
 * Extrae palabras clave de una petición para buscar en los chunks.
 * Filtra palabras comunes y extrae números de apartados específicos.
 */
function extraerPalabrasClave(peticion: string): string[] {
  const preguntaLimpia = peticion.toLowerCase()
    .replace(/[^\w\sáéíóúñü.-]/g, ' ') // Conservamos puntos y guiones para números de apartado
    .replace(/\s+/g, ' ')
    .trim();

  // Palabras comunes en español que no aportan valor para la búsqueda
  const palabrasComunes = new Set([
    'el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas',
    'de', 'del', 'en', 'con', 'por', 'para', 'que', 'se',
    'es', 'son', 'está', 'están', 'como', 'qué', 'cuál',
    'genera', 'crea', 'haz', 'dime', 'sobre', 'acerca', 
    'test', 'flashcards', 'preguntas', 'respuestas',
    'apartado', 'tema', 'capítulo', 'sección'
  ]);

  // Extraer palabras significativas (3+ caracteres, no comunes)
  const palabras = preguntaLimpia.split(' ')
    .filter(palabra => palabra.length >= 3 && !palabrasComunes.has(palabra));

  // Extraer números de apartados (ej: 5.5, 1.2.3)
  const numerosApartados = preguntaLimpia.match(/\d+\.\d+(\.\d+)?/g) || [];

  // Combinar y eliminar duplicados
  return Array.from(new Set([...palabras, ...numerosApartados]));
}

/**
 * Calcula la puntuación de relevancia de un chunk basado en las palabras clave.
 */
function calcularPuntuacionChunk(
  chunk: Chunk, 
  palabrasClave: string[], 
  config: ChunkSelectorConfig
): {
  score: number;
  matchedKeywords: string[];
  sectionMatches: number;
  contentMatches: number;
} {
  let puntuacion = 0;
  const matchedKeywords: string[] = [];
  let sectionMatches = 0;
  let contentMatches = 0;

  const contenidoLower = chunk.content.toLowerCase();
  const seccionesLower = chunk.metadata.detectedSections.join(' ').toLowerCase();

  palabrasClave.forEach(palabra => {
    // Crear regex para buscar la palabra completa
    const regex = new RegExp(`\\b${palabra.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'gi');
    
    // Buscar coincidencias en títulos de sección (mayor peso)
    const coincidenciasSecciones = (seccionesLower.match(regex) || []).length;
    if (coincidenciasSecciones > 0) {
      puntuacion += coincidenciasSecciones * config.sectionTitleWeight;
      sectionMatches += coincidenciasSecciones;
      matchedKeywords.push(palabra);
    }

    // Buscar coincidencias en el contenido (menor peso)
    const coincidenciasContenido = (contenidoLower.match(regex) || []).length;
    if (coincidenciasContenido > 0) {
      puntuacion += coincidenciasContenido * config.contentWeight;
      contentMatches += coincidenciasContenido;
      if (!matchedKeywords.includes(palabra)) {
        matchedKeywords.push(palabra);
      }
    }
  });

  return {
    score: puntuacion,
    matchedKeywords,
    sectionMatches,
    contentMatches
  };
}

/**
 * Selecciona los chunks más relevantes basados en la petición del usuario.
 * Busca tanto en el contenido como en los metadatos de las secciones.
 * 
 * @param chunks Array de chunks a evaluar
 * @param peticion Petición del usuario con instrucciones específicas
 * @param config Configuración del selector (opcional)
 * @returns Resultado de la selección con chunks ordenados por relevancia
 */
export function seleccionarChunksRelevantes(
  chunks: Chunk[], 
  peticion: string, 
  config: ChunkSelectorConfig = DEFAULT_CHUNK_SELECTOR_CONFIG
): ChunkSelectionResult {
  // Si no hay petición, devolver array vacío
  if (!peticion || peticion.trim() === '') {
    console.log('📝 No hay petición específica, no se seleccionan chunks');
    return {
      selectedChunks: [],
      totalChunks: chunks.length,
      selectionCriteria: {
        keywords: [],
        maxChunks: config.topN,
        minScore: config.relevanceThreshold
      },
      scoringInfo: []
    };
  }

  const palabrasClave = extraerPalabrasClave(peticion);

  // Si no hay palabras clave útiles, devolver array vacío
  if (palabrasClave.length === 0) {
    console.log('📝 No se encontraron palabras clave útiles, no se seleccionan chunks');
    return {
      selectedChunks: [],
      totalChunks: chunks.length,
      selectionCriteria: {
        keywords: [],
        maxChunks: config.topN,
        minScore: config.relevanceThreshold
      },
      scoringInfo: []
    };
  }

  console.log(`🔍 Palabras clave extraídas: ${palabrasClave.join(', ')}`);

  // Calcular puntuación para cada chunk
  const chunksConPuntuacion = chunks.map((chunk, index) => {
    const scoring = calcularPuntuacionChunk(chunk, palabrasClave, config);
    return {
      chunk,
      chunkIndex: index,
      ...scoring
    };
  });

  // Filtrar chunks con puntuación mínima y ordenar por relevancia
  const chunksRelevantes = chunksConPuntuacion
    .filter(item => item.score >= config.relevanceThreshold)
    .sort((a, b) => b.score - a.score);

  // Si no se encuentra ninguno relevante, devolver array vacío
  // (el código que llama debe manejar este caso)
  if (chunksRelevantes.length === 0) {
    console.warn('⚠️ No se encontraron chunks relevantes para la petición');
    return {
      selectedChunks: [],
      totalChunks: chunks.length,
      selectionCriteria: {
        keywords: palabrasClave,
        maxChunks: config.topN,
        minScore: config.relevanceThreshold
      },
      scoringInfo: []
    };
  }

  // Seleccionar los top N chunks más relevantes
  const selectedItems = chunksRelevantes.slice(0, config.topN);

  console.log(`✅ Seleccionados ${selectedItems.length} chunks de ${chunks.length} total`);
  console.log(`📊 Puntuaciones: ${selectedItems.map(item => item.score.toFixed(2)).join(', ')}`);

  return {
    selectedChunks: selectedItems.map(item => item.chunk),
    totalChunks: chunks.length,
    selectionCriteria: {
      keywords: palabrasClave,
      maxChunks: config.topN,
      minScore: config.relevanceThreshold
    },
    scoringInfo: selectedItems.map(item => ({
      chunkIndex: item.chunkIndex,
      score: item.score,
      matchedKeywords: item.matchedKeywords,
      sectionMatches: item.sectionMatches,
      contentMatches: item.contentMatches
    }))
  };
}

/**
 * Función de utilidad para obtener información de debugging sobre la selección
 */
export function logChunkSelectionInfo(result: ChunkSelectionResult): void {
  console.log('🔍 Información de Selección de Chunks:');
  console.log(`   Palabras clave: ${result.selectionCriteria.keywords.join(', ')}`);
  console.log(`   Chunks evaluados: ${result.totalChunks}`);
  console.log(`   Chunks seleccionados: ${result.selectedChunks.length}`);

  result.scoringInfo.forEach((info, index) => {
    console.log(`   Chunk ${info.chunkIndex}: score=${info.score.toFixed(2)}, ` +
                `keywords=[${info.matchedKeywords.join(', ')}], ` +
                `sección=${info.sectionMatches}, contenido=${info.contentMatches}`);
  });
}

chunking.ts

/**
 * Tipos TypeScript para el Sistema de Chunking Inteligente
 * 
 * Este archivo define todas las interfaces y tipos relacionados con
 * la división inteligente de documentos en chunks para procesamiento por IA.
 */

// Re-exportar tipos básicos desde textProcessing para mantener consistencia
import type {
  Chunk,
  ChunkMetadata,
  ChunkingConfig
} from '@/lib/utils/textProcessing';

export type {
  Chunk,
  ChunkMetadata,
  ChunkingConfig
};

/**
 * Resultado del procesamiento de un documento por el sistema de chunking
 */
export interface DocumentChunkingResult {
  /** Indica si el documento fue dividido en chunks */
  wasChunked: boolean;
  
  /** Contenido original o array de chunks */
  content: string | Chunk[];
  
  /** Estadísticas del proceso de chunking */
  stats: ChunkingStats;
  
  /** Configuración utilizada para el chunking */
  configUsed: ChunkingConfig;
  
  /** Información del documento original */
  sourceDocument: {
    id?: string;
    title: string;
    originalSize: number;
  };
}

/**
 * Estadísticas del proceso de chunking
 */
export interface ChunkingStats {
  /** Número total de chunks generados */
  totalChunks: number;
  
  /** Tamaño promedio de los chunks */
  averageChunkSize: number;
  
  /** Tamaño del chunk más pequeño */
  minChunkSize: number;
  
  /** Tamaño del chunk más grande */
  maxChunkSize: number;
  
  /** Tamaño total del contenido */
  totalContentSize: number;
  
  /** Número de secciones detectadas */
  sectionsDetected: number;
  
  /** Tipo de división utilizada */
  chunkingStrategy: 'section' | 'paragraph' | 'sentence' | 'fixed' | 'none';
  
  /** Tiempo de procesamiento en milisegundos */
  processingTimeMs: number;
}

/**
 * Configuración específica por tipo de contenido
 */
export interface ContentTypeChunkingConfig {
  /** Configuración para documentos de temario */
  temario: ChunkingConfig;
  
  /** Configuración para documentos legales */
  legal: ChunkingConfig;
  
  /** Configuración para documentos técnicos */
  tecnico: ChunkingConfig;
  
  /** Configuración por defecto */
  default: ChunkingConfig;
}

/**
 * Contexto de chunking para prompts de IA
 */
export interface ChunkingContext {
  /** Número del chunk actual */
  currentChunk: number;
  
  /** Total de chunks */
  totalChunks: number;
  
  /** Indica si hay chunks anteriores */
  hasPreviousChunks: boolean;
  
  /** Indica si hay chunks posteriores */
  hasNextChunks: boolean;
  
  /** Secciones detectadas en el documento completo */
  documentSections: string[];
  
  /** Tipo de estrategia de chunking utilizada */
  chunkingStrategy: ChunkingStats['chunkingStrategy'];
  
  /** Información adicional para el contexto */
  additionalContext?: string;
}

/**
 * Opciones para el procesamiento de documentos
 */
export interface DocumentProcessingOptions {
  /** Habilitar o deshabilitar chunking */
  enableChunking: boolean;
  
  /** Configuración personalizada de chunking */
  chunkingConfig?: Partial<ChunkingConfig>;
  
  /** Tipo de contenido para usar configuración específica */
  contentType?: keyof ContentTypeChunkingConfig;
  
  /** Forzar chunking incluso para documentos pequeños */
  forceChunking?: boolean;
  
  /** Incluir estadísticas detalladas en el resultado */
  includeStats?: boolean;
  
  /** Callback para progreso del chunking */
  onProgress?: (progress: ChunkingProgress) => void;
}

/**
 * Información de progreso durante el chunking
 */
export interface ChunkingProgress {
  /** Fase actual del proceso */
  phase: 'detecting_sections' | 'dividing_content' | 'adding_overlap' | 'finalizing';
  
  /** Porcentaje de completado (0-100) */
  percentage: number;
  
  /** Mensaje descriptivo del progreso */
  message: string;
  
  /** Chunks procesados hasta el momento */
  chunksProcessed: number;
  
  /** Estimación de chunks totales */
  estimatedTotalChunks: number;
}

/**
 * Resultado de la combinación de múltiples chunks procesados por IA
 */
export interface ChunkCombinationResult<T = any> {
  /** Resultado combinado final */
  combinedResult: T;
  
  /** Resultados individuales de cada chunk */
  individualResults: T[];
  
  /** Estadísticas de la combinación */
  combinationStats: {
    /** Número de chunks procesados */
    chunksProcessed: number;
    
    /** Elementos duplicados eliminados */
    duplicatesRemoved: number;
    
    /** Tiempo total de procesamiento */
    totalProcessingTimeMs: number;
    
    /** Estrategia de combinación utilizada */
    combinationStrategy: string;
  };
  
  /** Metadatos adicionales */
  metadata: {
    /** Configuración de chunking utilizada */
    chunkingConfig: ChunkingConfig;
    
    /** Contexto de chunking */
    chunkingContext: ChunkingContext[];
    
    /** Información del documento fuente */
    sourceDocument: {
      id?: string;
      title: string;
      totalSize: number;
    };
  };
}

/**
 * Configuración para la combinación de resultados
 */
export interface ResultCombinationConfig {
  /** Estrategia de combinación */
  strategy: 'merge' | 'deduplicate' | 'prioritize_first' | 'prioritize_last' | 'custom';
  
  /** Función personalizada de combinación */
  customCombiner?: <T>(results: T[]) => T;
  
  /** Configuración para deduplicación */
  deduplication?: {
    /** Función para determinar si dos elementos son duplicados */
    isDuplicate: <T>(a: T, b: T) => boolean;
    
    /** Función para resolver conflictos entre duplicados */
    resolveDuplicate?: <T>(a: T, b: T) => T;
  };
  
  /** Límite máximo de elementos en el resultado final */
  maxResults?: number;
  
  /** Incluir metadatos de combinación */
  includeMetadata?: boolean;
}

/**
 * Error específico del sistema de chunking
 */
export class ChunkingError extends Error {
  constructor(
    message: string,
    public readonly code: ChunkingErrorCode,
    public readonly details?: any
  ) {
    super(message);
    this.name = 'ChunkingError';
  }
}

/**
 * Códigos de error del sistema de chunking
 */
export enum ChunkingErrorCode {
  INVALID_CONTENT = 'INVALID_CONTENT',
  CONFIG_ERROR = 'CONFIG_ERROR',
  SECTION_DETECTION_FAILED = 'SECTION_DETECTION_FAILED',
  CHUNK_TOO_LARGE = 'CHUNK_TOO_LARGE',
  PROCESSING_TIMEOUT = 'PROCESSING_TIMEOUT',
  COMBINATION_FAILED = 'COMBINATION_FAILED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Validador de configuración de chunking
 */
export interface ChunkingConfigValidator {
  /** Valida una configuración de chunking */
  validate(config: Partial<ChunkingConfig>): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  };
  
  /** Normaliza y completa una configuración parcial */
  normalize(config: Partial<ChunkingConfig>): ChunkingConfig;
}

/**
 * Métricas de rendimiento del sistema de chunking
 */
export interface ChunkingPerformanceMetrics {
  /** Tiempo promedio de chunking por documento */
  averageChunkingTime: number;
  
  /** Tiempo promedio de procesamiento por chunk */
  averageProcessingTimePerChunk: number;
  
  /** Número total de documentos procesados */
  totalDocumentsProcessed: number;
  
  /** Número total de chunks generados */
  totalChunksGenerated: number;
  
  /** Distribución de tamaños de chunks */
  chunkSizeDistribution: {
    small: number;    // < 10k caracteres
    medium: number;   // 10k-30k caracteres
    large: number;    // > 30k caracteres
  };
  
  /** Tasa de éxito del chunking */
  successRate: number;
  
  /** Errores más comunes */
  commonErrors: Array<{
    code: ChunkingErrorCode;
    count: number;
    percentage: number;
  }>;
}

/**
 * Configuración de logging para el sistema de chunking
 */
export interface ChunkingLoggingConfig {
  /** Nivel de logging */
  level: 'debug' | 'info' | 'warn' | 'error';
  
  /** Incluir estadísticas detalladas en los logs */
  includeStats: boolean;
  
  /** Incluir contenido de chunks en logs (solo para debug) */
  includeContent: boolean;
  
  /** Función personalizada de logging */
  customLogger?: (level: string, message: string, data?: any) => void;
}

testgenerator.ts

import { prepararDocumentos } from './geminiClient';
import { PROMPT_TESTS } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';
import { type Chunk } from '@/lib/utils/textProcessing';
import { seleccionarChunksRelevantes } from '@/lib/utils/chunkSelector';
import * as Sentry from "@sentry/nextjs";

/**
 * Genera un test con preguntas de opción múltiple a partir de los documentos
 */
export async function generarTest(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  cantidad: number = 10,
  instrucciones?: string
): Promise<{
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}[]> {
  try {
    // Preparar el contenido de los documentos
    const resultadoDocumentos = prepararDocumentos(documentos);

    // Verificar si se usó chunking
    if (resultadoDocumentos.wasChunked && Array.isArray(resultadoDocumentos.content)) {
      console.log(`🧪 Procesando test con chunking: ${resultadoDocumentos.content.length} chunks`);
      return await procesarTestConChunks(resultadoDocumentos.content, cantidad, instrucciones, documentos);
    } else {
      // Procesamiento tradicional para documentos sin chunking
      const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
        ? resultadoDocumentos.content.join('\n\n')
        : resultadoDocumentos.content;

      if (!contenidoDocumentos) {
        throw new Error("No se han proporcionado documentos para generar el test.");
      }

      console.log(`🧪 Procesando test sin chunking`);
      return await procesarTestSinChunks(contenidoDocumentos, cantidad, instrucciones);
    }


  } catch (error) {
    console.error('Error al generar test:', error);
    throw error;
  }
}

/**
 * Procesa test con chunking - selecciona chunks relevantes y los combina en un solo contexto
 */
async function procesarTestConChunks(
  chunks: Chunk[],
  cantidad: number,
  instrucciones?: string,
  documentos?: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<{
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}[]> {
  // --- INICIO DE LA NUEVA LÓGICA DE SELECCIÓN ---
  console.log(`🧪 Seleccionando chunks relevantes para la petición: "${instrucciones}"`);
  const selectionResult = seleccionarChunksRelevantes(chunks, instrucciones || '');

  if (selectionResult.selectedChunks.length === 0) {
    throw new Error("No se pudo encontrar contenido relevante para tu petición. Intenta ser más específico.");
  }

  console.log(`🧪 Chunks relevantes encontrados: ${selectionResult.selectedChunks.length} de ${chunks.length}`);
  // --- FIN DE LA NUEVA LÓGICA DE SELECCIÓN ---

  // PASO 2 (NUEVO): Combinar el contenido de los chunks seleccionados en un solo contexto
  console.log(`🔗 Uniendo ${selectionResult.selectedChunks.length} chunks en un solo contexto.`);
  const combinedContent = selectionResult.selectedChunks
    .map(chunk => chunk.content)
    .join('\n\n---\n\n'); // Un separador claro ayuda a la IA

  // PASO 3: Preparar el prompt para una ÚNICA llamada
  let prompt = PROMPT_TESTS
    .replace('{documentos}', combinedContent) // Usar el contenido combinado
    .replace('{cantidad}', cantidad.toString());

  if (instrucciones) {
    prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
  } else {
    prompt = prompt.replace('{instrucciones}', '');
  }

  // PASO 4: Hacer UNA SOLA llamada a la IA
  const config = getOpenAIConfig('TESTS');
  const messages = [{ role: 'user' as const, content: prompt }];
  const responseText = await llamarOpenAI(messages, {
    ...config,
    activityName: `Generación [Combinada] de Test (${cantidad} preguntas)`
  });

  // PASO 5: Parsear la respuesta directamente (ya no se necesita combinar)
  const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);
  if (!jsonMatch) {
    throw new Error("La IA no devolvió un formato JSON válido.");
  }

  const testJson = jsonMatch[0];

  // Corregir errores comunes de formato antes del parsing
  const testJsonCorregido = testJson
    .replace(/"opcion([abcd])"/g, '"opcion_$1"')
    .replace(/"opciona"/g, '"opcion_a"')
    .replace(/"opcionb"/g, '"opcion_b"')
    .replace(/"opcionc"/g, '"opcion_c"')
    .replace(/"opciond"/g, '"opcion_d"');

  const preguntas = JSON.parse(testJsonCorregido);

  // Validar formato básico
  if (!Array.isArray(preguntas) || preguntas.length === 0) {
    throw new Error("La IA no generó preguntas válidas.");
  }

  // Validar estructura de cada pregunta
  const preguntasValidas = preguntas.filter(pregunta =>
    pregunta.pregunta &&
    pregunta.opcion_a &&
    pregunta.opcion_b &&
    pregunta.opcion_c &&
    pregunta.opcion_d &&
    pregunta.respuesta_correcta
  );

  if (preguntasValidas.length === 0) {
    throw new Error("Ninguna de las preguntas generadas tiene el formato correcto.");
  }

  console.log(`🧪 Test generado con chunking combinado: ${preguntasValidas.length} preguntas finales`);
  return preguntasValidas;
}

/**
 * Procesa test sin chunking - método tradicional
 */
async function procesarTestSinChunks(
  contenidoDocumentos: string,
  cantidad: number,
  instrucciones?: string
): Promise<{
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}[]> {
  // Construir el prompt para la IA usando el prompt personalizado
  let prompt = PROMPT_TESTS
    .replace('{documentos}', contenidoDocumentos)
    .replace('{cantidad}', cantidad.toString());

  // Añadir instrucciones adicionales si se proporcionan
  if (instrucciones) {
    prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
  } else {
    prompt = prompt.replace('{instrucciones}', '');
  }

  // Obtener configuración específica para tests
  const config = getOpenAIConfig('TESTS');

  console.log(`🧪 Generando test con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

  // Generar el test usando OpenAI con la configuración correcta
  const messages = [{ role: 'user' as const, content: prompt }];
  const responseText = await llamarOpenAI(messages, {
    ...config,
    activityName: `Generación de Test (${cantidad || 'N/A'} preguntas)`
  });

  // Extraer el JSON de la respuesta
  const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);

  if (!jsonMatch) {
    console.log('❌ No se encontró JSON en la respuesta. Respuesta recibida:', responseText.substring(0, 500));
    throw new Error("No se pudo extraer el formato JSON de la respuesta.");
  }

  const testJson = jsonMatch[0];

  // Corregir errores comunes de formato antes del parsing
  const testJsonCorregido = testJson
    .replace(/"opcion([abcd])"/g, '"opcion_$1"')  // Corregir opcionX -> opcion_X
    .replace(/"opciona"/g, '"opcion_a"')
    .replace(/"opcionb"/g, '"opcion_b"')
    .replace(/"opcionc"/g, '"opcion_c"')
    .replace(/"opciond"/g, '"opcion_d"');

  const preguntas = JSON.parse(testJsonCorregido);

  // Verificar la cantidad de preguntas generadas
  console.log(`📊 Preguntas generadas: ${preguntas.length} de ${cantidad} solicitadas`);

  // Validar el formato
  if (!Array.isArray(preguntas) || preguntas.length === 0) {
    throw new Error("El formato de las preguntas generadas no es válido.");
  }

  if (preguntas.length !== cantidad) {
    console.log(`⚠️ Advertencia: Se generaron ${preguntas.length} preguntas en lugar de ${cantidad}`);
  }

  // Validar que cada pregunta tiene el formato correcto
  preguntas.forEach((pregunta: any, index: number) => {
    if (!pregunta.pregunta || !pregunta.opcion_a || !pregunta.opcion_b ||
        !pregunta.opcion_c || !pregunta.opcion_d || !pregunta.respuesta_correcta) {
      throw new Error(`La pregunta ${index + 1} no tiene el formato correcto.`);
    }

    // Asegurarse de que la respuesta correcta es una de las opciones válidas
    if (!['a', 'b', 'c', 'd'].includes(pregunta.respuesta_correcta)) {
      throw new Error(`La respuesta correcta de la pregunta ${index + 1} no es válida.`);
    }
  });

  return preguntas;
}
