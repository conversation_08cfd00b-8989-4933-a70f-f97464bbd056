import { useFirstVisitContext } from '@/contexts/FirstVisitContext';

// Tipo para las pantallas disponibles
type ScreenType = 'tests' | 'flashcards' | 'mindMaps';

// Hook personalizado para simplificar el uso del FirstVisitContext
export function useFirstVisit() {
  const context = useFirstVisitContext();

  return {
    /**
     * Verifica si es la primera visita a una pantalla específica
     * @param screen - La pantalla a verificar ('tests', 'flashcards', 'mindMaps')
     * @returns true si es la primera visita, false si ya fue visitada
     */
    isFirstVisit: (screen: ScreenType) => context.isFirstVisit(screen),

    /**
     * Marca una pantalla como visitada
     * @param screen - La pantalla a marcar como visitada
     */
    markAsVisited: (screen: ScreenType) => context.markAsVisited(screen),

    /**
     * Resetea toda la sesión (útil para testing/desarrollo)
     */
    resetSession: () => context.resetSession(),
  };
}
