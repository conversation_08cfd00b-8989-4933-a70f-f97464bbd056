'use client';

import React from 'react';
import { AuthProvider } from '@/contexts/AuthContext';
import { BackgroundTasksProvider } from '@/contexts/BackgroundTasksContext';
import { FirstVisitProvider } from '@/contexts/FirstVisitContext';
import AuthManager from '../../auth/components/AuthManager';
import NotificationManager from '@/features/auth/components/NotificationManager';
import BackgroundTasksPanel from '@/components/ui/BackgroundTasksPanel';
import { Toaster } from 'react-hot-toast'; // Importar Toaster
import CookieBanner from '@/components/ui/CookieBanner';

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <BackgroundTasksProvider>
      <AuthProvider>
        <FirstVisitProvider>
          <AuthManager />
          <NotificationManager />
          <Toaster
            position="top-right" // Posición de los toasts
            toastOptions={{
              // Opciones por defecto para los toasts
              duration: 5000, // Duración de 5 segundos
              style: {
                background: '#363636', // Estilo oscuro
                color: '#fff',
              },
              success: {
                duration: 3000,
                style: {
                  background: '#10b981',
                  color: '#fff',
                },
              },
              error: {
                duration: 5000,
                style: {
                  background: '#ef4444',
                  color: '#fff',
                },
              }
            }}
          />
          <BackgroundTasksPanel />
          {children}
          <CookieBanner />
        </FirstVisitProvider>
      </AuthProvider>
    </BackgroundTasksProvider>
  );
}
