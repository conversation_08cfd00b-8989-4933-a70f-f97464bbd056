import React from 'react';
import { Fi<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiX as FiXIcon, FiTrendingUp } from 'react-icons/fi';

interface EstadisticasTest {
  totalPreguntas: number;
  totalCorrectas: number;
  totalIncorrectas: number;
  porcentajeAcierto: number;
  fechasRealizacion: string[];
  preguntasMasFalladas: {
    preguntaId: string;
    pregunta: string;
    totalAciertos: number;
    totalFallos: number;
  }[];
}

interface TestDetailedStatisticsProps {
  estadisticas: EstadisticasTest;
  testTitulo: string;
  onClose: () => void;
}

const TestDetailedStatistics: React.FC<TestDetailedStatisticsProps> = ({
  estadisticas,
  testTitulo,
  onClose
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Estadísticas del Test: {testTitulo}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Estadísticas principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiClock className="text-blue-600 mr-2 text-xl" />
              <h4 className="font-semibold">Veces Realizado</h4>
            </div>
            <p className="text-3xl font-bold text-blue-700">{estadisticas.fechasRealizacion.length}</p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiCheck className="text-green-600 mr-2 text-xl" />
              <h4 className="font-semibold">Respuestas Correctas</h4>
            </div>
            <p className="text-3xl font-bold text-green-700">{estadisticas.totalCorrectas}</p>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiXIcon className="text-red-600 mr-2 text-xl" />
              <h4 className="font-semibold">Respuestas Incorrectas</h4>
            </div>
            <p className="text-3xl font-bold text-red-700">{estadisticas.totalIncorrectas}</p>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiTrendingUp className="text-purple-600 mr-2 text-xl" />
              <h4 className="font-semibold">Porcentaje de Acierto</h4>
            </div>
            <p className="text-3xl font-bold text-purple-700">{estadisticas.porcentajeAcierto.toFixed(1)}%</p>
          </div>
        </div>

        {/* Preguntas más falladas */}
        {estadisticas.preguntasMasFalladas.length > 0 && (
          <div className="mb-6">
            <h4 className="text-lg font-semibold mb-3">Preguntas con Más Fallos</h4>
            <div className="space-y-3">
              {estadisticas.preguntasMasFalladas.map((pregunta, index) => (
                <div key={pregunta.preguntaId} className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="flex justify-between items-start">
                    <div className="flex items-start">
                      <div className="text-lg font-bold text-red-600 mr-3">#{index + 1}</div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{pregunta.pregunta}</p>
                        <div className="flex items-center space-x-4 mt-2 text-sm">
                          <span className="text-red-600">
                            <FiXIcon className="inline mr-1" /> {pregunta.totalFallos} fallos
                          </span>
                          <span className="text-green-600">
                            <FiCheck className="inline mr-1" /> {pregunta.totalAciertos} aciertos
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestDetailedStatistics;
