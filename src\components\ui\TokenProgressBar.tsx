'use client';

import React from 'react';

interface TokenProgressBarProps {
  used: number | null | undefined;
  limit: number | null | undefined;
  percentage: number | null | undefined;
  remaining: number | null | undefined;
}

export default function TokenProgressBar({ used, limit, percentage, remaining }: TokenProgressBarProps) {
  // Validaciones defensivas para evitar errores con valores null/undefined
  const safeUsed = used || 0;
  const safeLimit = limit || 0;
  const safePercentage = percentage || 0;
  const safeRemaining = remaining || 0;

  // Determinar color según el porcentaje de uso
  const getProgressColor = (percentage: number) => {
    if (percentage < 50) return 'bg-green-500';
    if (percentage < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getProgressBgColor = (percentage: number) => {
    if (percentage < 50) return 'bg-green-100';
    if (percentage < 80) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const formatTokens = (tokens: number | null | undefined) => {
    // Validación defensiva
    const validTokens = tokens || 0;

    if (validTokens >= 1000000) {
      return `${(validTokens / 1000000).toFixed(1)}M`;
    }
    if (validTokens >= 1000) {
      return `${(validTokens / 1000).toFixed(1)}K`;
    }
    return validTokens.toLocaleString();
  };

  return (
    <div className="space-y-3">
      {/* Título y porcentaje */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-700">Uso de Tokens</h3>
        <span className={`text-sm font-semibold ${
          safePercentage < 50 ? 'text-green-600' :
          safePercentage < 80 ? 'text-yellow-600' :
          'text-red-600'
        }`}>
          {safePercentage}%
        </span>
      </div>

      {/* Barra de progreso */}
      <div className={`w-full ${getProgressBgColor(safePercentage)} rounded-full h-3`}>
        <div
          className={`h-3 rounded-full transition-all duration-300 ${getProgressColor(safePercentage)}`}
          style={{ width: `${Math.min(safePercentage, 100)}%` }}
        />
      </div>

      {/* Información detallada */}
      <div className="flex items-center justify-between text-xs text-gray-600">
        <span>
          <strong>{formatTokens(safeUsed)}</strong> usados
        </span>
        <span>
          <strong>{formatTokens(safeRemaining)}</strong> restantes
        </span>
      </div>

      {/* Límite total */}
      <div className="text-center">
        <span className="text-xs text-gray-500">
          Límite mensual: <strong>{formatTokens(safeLimit)}</strong> tokens
        </span>
      </div>

      {/* Advertencia si está cerca del límite */}
      {safePercentage >= 80 && (
        <div className={`p-2 rounded-lg text-xs ${
          safePercentage >= 95 ? 'bg-red-50 text-red-700 border border-red-200' :
          'bg-yellow-50 text-yellow-700 border border-yellow-200'
        }`}>
          {safePercentage >= 95 ? (
            <span>⚠️ Límite casi alcanzado. Considera comprar más tokens.</span>
          ) : (
            <span>⚠️ Te estás acercando al límite mensual de tokens.</span>
          )}
        </div>
      )}
    </div>
  );
}
