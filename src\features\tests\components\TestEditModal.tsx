import React, { useState, useEffect } from 'react';
import { FiX, FiSave } from 'react-icons/fi';
import { Test } from '@/lib/supabase/supabaseClient';
import { actualizarTest } from '@/lib/supabase/testsService';
import { toast } from 'react-hot-toast';

interface TestEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  test: Test;
  onSave: (testActualizado: Test) => void;
}

const TestEditModal: React.FC<TestEditModalProps> = ({
  isOpen,
  onClose,
  test,
  onSave
}) => {
  const [titulo, setTitulo] = useState('');
  const [descripcion, setDescripcion] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Cargar datos del test cuando se abre el modal
  useEffect(() => {
    if (isOpen && test) {
      setTitulo(test.titulo);
      setDescripcion(test.descripcion || '');
    }
  }, [isOpen, test]);

  const handleSave = async () => {
    if (!titulo.trim()) {
      toast.error('El título del test es obligatorio');
      return;
    }

    setIsLoading(true);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Actualizando test...');

      const testActualizado = await actualizarTest(
        test.id,
        titulo.trim(),
        descripcion.trim() || undefined
      );

      if (testActualizado) {
        toast.success('Test actualizado correctamente', { id: loadingToastId });
        onSave(testActualizado);
        onClose();
      } else {
        toast.error('Error al actualizar el test', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al actualizar test:', error);
      toast.error('Error inesperado al actualizar el test', { id: loadingToastId });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (!isLoading) {
      onClose();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape' && !isLoading) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onKeyDown={handleKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="test-edit-title"
    >
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 
            id="test-edit-title"
            className="text-lg font-semibold text-gray-900"
          >
            Editar Test
          </h3>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isLoading}
            aria-label="Cerrar modal"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Body */}
        <div className="p-6 space-y-4">
          {/* Campo Título */}
          <div>
            <label 
              htmlFor="test-titulo"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Título del Test *
            </label>
            <input
              id="test-titulo"
              type="text"
              value={titulo}
              onChange={(e) => setTitulo(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Ingresa el título del test"
              disabled={isLoading}
              maxLength={255}
              required
            />
          </div>

          {/* Campo Descripción */}
          <div>
            <label 
              htmlFor="test-descripcion"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Descripción (opcional)
            </label>
            <textarea
              id="test-descripcion"
              value={descripcion}
              onChange={(e) => setDescripcion(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
              placeholder="Describe brevemente el contenido del test"
              disabled={isLoading}
              maxLength={1000}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            disabled={isLoading || !titulo.trim()}
          >
            <FiSave size={16} />
            <span>{isLoading ? 'Guardando...' : 'Guardar'}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default TestEditModal;
