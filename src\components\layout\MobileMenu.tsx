'use client';

import React, { useEffect } from 'react';
import { FiX } from 'react-icons/fi';
import { TabType } from '@/types/ui';

/**
 * Componente de Menú Modal para Móvil
 * 
 * Proporciona un menú deslizable para dispositivos móviles con:
 * - Overlay con backdrop
 * - Animaciones de entrada/salida
 * - Lista de navegación optimizada para touch
 * - Cierre automático al seleccionar opción
 * 
 * Siguiendo los patrones definidos en ARCHITECTURE.md
 */

interface MenuItem {
  id: TabType | string;
  label: string;
  icon: React.ReactNode;
  color: string;
  children?: MenuItem[];
  isGroup?: boolean;
}

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  menuItems: MenuItem[];
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  onClose,
  menuItems,
  activeTab,
  onTabChange
}) => {
  // Prevenir scroll del body cuando el menú está abierto
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup al desmontar
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Cerrar menú con tecla Escape
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  const handleTabSelect = (tabId: TabType) => {
    onTabChange(tabId);
    onClose(); // Cerrar menú después de seleccionar
  };

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    const isActive = activeTab === item.id;
    const hasChildren = item.children && item.children.length > 0;

    // Si es un grupo, renderizar sus hijos directamente
    if (item.isGroup && hasChildren) {
      return (
        <div key={item.id} className="mb-4">
          <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
            {item.label}
          </div>
          {item.children?.map(child => renderMenuItem(child, level + 1))}
        </div>
      );
    }

    return (
      <button
        key={item.id}
        onClick={() => handleTabSelect(item.id as TabType)}
        className={`w-full flex items-center px-4 py-3 text-left transition-colors duration-200 ${
          isActive
            ? `text-white ${item.color} shadow-sm`
            : 'text-gray-700 hover:bg-gray-100'
        } ${level > 0 ? 'ml-4' : ''}`}
      >
        <div className="flex items-center space-x-3">
          <div className={`flex-shrink-0 ${isActive ? 'text-white' : 'text-gray-500'}`}>
            {item.icon}
          </div>
          <span className="font-medium">{item.label}</span>
        </div>
      </button>
    );
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Menú deslizable */}
      <div
        className={`lg:hidden fixed inset-y-0 left-0 w-80 max-w-sm bg-white shadow-xl z-50 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        role="dialog"
        aria-modal="true"
        aria-label="Menú de navegación"
      >
        {/* Header del menú */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Menú de Estudio
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label="Cerrar menú"
          >
            <FiX className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Lista de navegación */}
        <nav className="flex-1 overflow-y-auto py-4">
          <div className="space-y-1">
            {menuItems.map(item => renderMenuItem(item))}
          </div>
        </nav>
      </div>
    </>
  );
};

export default MobileMenu;
