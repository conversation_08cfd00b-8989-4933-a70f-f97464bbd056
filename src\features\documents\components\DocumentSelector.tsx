import React, { useEffect, useState, useImperativeHandle, forwardRef, useCallback } from 'react';
import Select from 'react-select';
import { Documento, obtenerDocumentos } from '../../../lib/supabase';

interface DocumentSelectorProps {
  onSelectionChange: (documento: Documento | null) => void;
}

export interface DocumentSelectorRef {
  recargarDocumentos: () => Promise<void>;
}

const DocumentSelector = forwardRef<DocumentSelectorRef, DocumentSelectorProps>(
  ({ onSelectionChange }, ref) => {
    const [documentos, setDocumentos] = useState<Documento[]>([]);
    const [selectedOption, setSelectedOption] = useState<any | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    const cargarDocumentos = useCallback(async () => {
      setIsLoading(true);
      try {
        const docs = await obtenerDocumentos();
        setDocumentos(docs);
      } catch (error) {
        console.error('Error al cargar documentos:', error);
      } finally {
        setIsLoading(false);
      }
    }, []);

    useEffect(() => {
      cargarDocumentos();
    }, [cargarDocumentos]);

    // Exponer la función de recarga a través de ref
    useImperativeHandle(ref, () => ({
      recargarDocumentos: cargarDocumentos
    }), [cargarDocumentos]);

    const handleChange = (selectedOption: any) => {
      setSelectedOption(selectedOption);

      // Encontrar el documento completo y pasarlo al padre
      const docSeleccionado = selectedOption
        ? documentos.find(doc => doc.id === selectedOption.value) || null
        : null;

      onSelectionChange(docSeleccionado);
    };

    const options = documentos.map(doc => ({
      value: doc.id,
      label: `${doc.numero_tema ? `Tema ${doc.numero_tema}: ` : ''}${doc.titulo} ${doc.categoria ? `(${doc.categoria})` : ''}`
    }));

    return (
      <div className="mb-3">
        <label className="block text-gray-700 text-sm font-medium mb-1">
          Selecciona el documento para trabajar:
        </label>
        <Select
          instanceId="document-selector"
          isLoading={isLoading}
          options={options}
          className="basic-single-select"
          classNamePrefix="select"
          placeholder="Selecciona un documento..."
          noOptionsMessage={() => "No hay documentos disponibles"}
          onChange={handleChange}
          value={selectedOption}
          isClearable={true}
          styles={{
            control: (provided) => ({
              ...provided,
              minHeight: '36px',
              fontSize: '14px'
            }),

            placeholder: (provided) => ({
              ...provided,
              fontSize: '14px'
            })
          }}
        />
        {!selectedOption && (
          <p className="text-red-500 text-xs italic mt-0.5">
            Debes seleccionar un documento para empezar.
          </p>
        )}
      </div>
    );
});

DocumentSelector.displayName = 'DocumentSelector';

export default DocumentSelector;
