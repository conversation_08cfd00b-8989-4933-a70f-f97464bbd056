# Configuración del Sistema Asíncrono de IA

Este documento describe cómo configurar las nuevas variables de entorno necesarias para el sistema de procesamiento asíncrono de IA.

## Variables de Entorno Requeridas

### 1. CRON_SECRET
**Descripción**: Clave secreta para proteger los endpoints de cron jobs.
**Formato**: String aleatorio seguro
**Ejemplo**: `your_secure_random_string_for_cron_jobs`

**Cómo generar**:
```bash
# Opción 1: Usar openssl
openssl rand -hex 32

# Opción 2: Usar Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Opción 3: Usar un generador online
# https://www.random.org/strings/
```

### 2. NEXT_PUBLIC_APP_URL
**Descripción**: URL base de tu aplicación (necesaria para que el dispatcher llame al worker).
**Formato**: URL completa con protocolo
**Ejemplos**:
- Desarrollo: `http://localhost:3000`
- Producción: `https://tu-app.vercel.app`

## Configuración en Vercel

1. Ve a tu proyecto en Vercel Dashboard
2. Navega a Settings > Environment Variables
3. Añade las siguientes variables:

```
CRON_SECRET=tu_clave_secreta_generada
NEXT_PUBLIC_APP_URL=https://tu-app.vercel.app
```

## Configuración Local

1. Copia `.env.example` a `.env.local`:
```bash
cp .env.example .env.local
```

2. Edita `.env.local` y configura las variables:
```env
CRON_SECRET=tu_clave_secreta_generada
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Verificación

Para verificar que todo está configurado correctamente:

1. **Desarrollo**: Las tareas deberían aparecer en la tabla `ai_tasks` de Supabase
2. **Producción**: Los cron jobs aparecerán en el dashboard de Vercel bajo "Functions"

## Troubleshooting

### Error: "No autorizado" en los endpoints
- Verifica que `CRON_SECRET` esté configurado correctamente
- Asegúrate de que la variable esté disponible tanto en el dispatcher como en el worker

### Error: "Worker URL no encontrado"
- Verifica que `NEXT_PUBLIC_APP_URL` esté configurado correctamente
- En producción, debe ser la URL de tu dominio de Vercel

### Las tareas no se procesan
- Verifica que los cron jobs estén activos en Vercel
- Revisa los logs de las funciones en el dashboard de Vercel
- Comprueba que la tabla `ai_tasks` existe en Supabase
