import React from 'react';
import { FiX, FiClock, FiHelpCircle, FiRefreshCw, FiBookOpen, FiLoader } from 'react-icons/fi';
import { TipoEstudio, OpcionEstudio, EstadisticasColeccion } from './types';

interface FlashcardStudyOptionsProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectStudyType: (tipo: TipoEstudio) => void;
  estadisticas?: EstadisticasColeccion | null;
  isLoading?: boolean;
}

const FlashcardStudyOptions: React.FC<FlashcardStudyOptionsProps> = ({
  isOpen,
  onClose,
  onSelectStudyType,
  estadisticas,
  isLoading = false
}) => {
  const opcionesEstudio: OpcionEstudio[] = [
    {
      tipo: 'dificiles',
      label: 'Más difíciles',
      descripcion: 'Tarjetas que has marcado como difíciles más frecuentemente',
      icon: <FiHelpCircle className="text-red-600" />,
      color: 'red-600',
      bgColor: 'bg-red-100',
      hoverBgColor: 'hover:bg-red-200'
    },
    {
      tipo: 'aleatorias',
      label: 'Aleatorias',
      descripcion: 'Selección aleatoria de tarjetas de la colección',
      icon: <FiRefreshCw className="text-purple-600" />,
      color: 'purple-600',
      bgColor: 'bg-purple-100',
      hoverBgColor: 'hover:bg-purple-200'
    },
    {
      tipo: 'no-recientes',
      label: 'No estudiadas recientemente',
      descripcion: 'Tarjetas que no has revisado en mucho tiempo',
      icon: <FiClock className="text-orange-600" />,
      color: 'orange-600',
      bgColor: 'bg-orange-100',
      hoverBgColor: 'hover:bg-orange-200'
    },
    {
      tipo: 'nuevas',
      label: 'Nuevas',
      descripcion: 'Tarjetas que nunca has estudiado',
      icon: <FiBookOpen className="text-green-600" />,
      color: 'green-600',
      bgColor: 'bg-green-100',
      hoverBgColor: 'hover:bg-green-200'
    },
    {
      tipo: 'aprendiendo',
      label: 'En aprendizaje',
      descripcion: 'Tarjetas que estás aprendiendo actualmente',
      icon: <FiLoader className="text-yellow-600" />,
      color: 'yellow-600',
      bgColor: 'bg-yellow-100',
      hoverBgColor: 'hover:bg-yellow-200'
    }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Opciones de Estudio</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <FiX size={24} />
          </button>
        </div>

        <div className="mb-6">
          <p className="text-gray-600 mb-3">
            Elige el tipo de estudio que prefieras. Cada opción te permitirá enfocar tu aprendizaje de manera diferente:
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-800 font-medium">
              ℹ️ Importante: Estos estudios adicionales son complementarios y <strong>no afectan al algoritmo de repetición espaciada</strong>.
              Para el estudio oficial que cuenta para tu progreso, usa el botón "Estudiar" principal.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {opcionesEstudio.map((opcion) => (
            <button
              key={opcion.tipo}
              onClick={() => onSelectStudyType(opcion.tipo)}
              className={`p-4 border rounded-lg text-left transition-all duration-200 ${opcion.hoverBgColor} ${opcion.bgColor} border-gray-200 hover:border-gray-300 cursor-pointer relative z-10`}
              disabled={isLoading}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  {opcion.icon}
                </div>
                <div className="flex-1">
                  <h4 className={`font-medium text-${opcion.color} mb-1`}>
                    {opcion.label}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {opcion.descripcion}
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Cancelar
          </button>
        </div>
      </div>
    </div>
  );
};

export default FlashcardStudyOptions;
