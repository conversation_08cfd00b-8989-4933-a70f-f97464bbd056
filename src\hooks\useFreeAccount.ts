// src/hooks/useFreeAccount.ts
// Hook para gestión de cuentas gratuitas en el frontend

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export interface FreeAccountStatus {
  isActive: boolean;
  expiresAt: string | null;
  daysRemaining: number;
  hoursRemaining: number;
  progressPercentage: number;
  usage: {
    documents: number;
    tests: number;
    flashcards: number;
    mindMaps: number;
    tokens: number;
  };
  limits: {
    documents: number;
    tests: number;
    flashcards: number;
    mindMaps: number;
    tokens: number;
  };
  usagePercentages: Record<string, number>;
}

export interface FreeAccountAlert {
  type: 'error' | 'warning' | 'info';
  message: string;
  action: string;
}

export interface UsageWarning {
  feature: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface FreeAccountData {
  isFreeAccount: boolean;
  status: FreeAccountStatus | null;
  alerts: FreeAccountAlert[];
  usageWarnings: UsageWarning[];
  recommendations: string[];
  upgradeUrl: string;
  loading: boolean;
  error: string | null;
}

/**
 * Hook principal para gestión de cuentas gratuitas
 */
export function useFreeAccount(): FreeAccountData & {
  refresh: () => Promise<void>;
  canPerformAction: (feature: string, amount?: number) => boolean;
} {
  const { user, isLoading: authLoading } = useAuth();
  const [data, setData] = useState<FreeAccountData>({
    isFreeAccount: false,
    status: null,
    alerts: [],
    usageWarnings: [],
    recommendations: [],
    upgradeUrl: '/upgrade-plan',
    loading: true,
    error: null
  });

  const fetchStatus = useCallback(async () => {
    if (!user || authLoading) {
      setData(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      setData(prev => ({ ...prev, loading: true, error: null }));

      const response = await fetch('/api/auth/free-account-status');
      const result = await response.json();

      if (!response.ok) {
        if (response.status === 404) {
          // Mantener manejo de 404 para errores genuinos (endpoint no encontrado, etc.)
          setData({
            isFreeAccount: false,
            status: null,
            alerts: [],
            usageWarnings: [],
            recommendations: [],
            upgradeUrl: '/upgrade-plan',
            loading: false,
            error: null
          });
          return;
        }
        throw new Error(result.error || 'Error obteniendo estado');
      }

      // Ahora la API siempre devuelve 200 OK, pero result.isFreeAccount determina el tipo de cuenta
      setData({
        isFreeAccount: result.isFreeAccount, // false para usuarios con plan de pago
        status: result.status, // null si isFreeAccount es false
        alerts: result.alerts || [],
        usageWarnings: result.usageWarnings || [],
        recommendations: result.recommendations || [],
        upgradeUrl: result.upgradeUrl || '/upgrade-plan',
        loading: false,
        error: null
      });

    } catch (error) {
      console.error('Error obteniendo estado de cuenta gratuita:', error);
      setData(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      }));
    }
  }, [user, authLoading]);

  // NOTA: incrementUsage ha sido eliminado porque el incremento de uso
  // ahora se hace automáticamente en el backend después de operaciones exitosas

  const canPerformAction = useCallback((feature: string, amount: number = 1): boolean => {
    if (!data.isFreeAccount || !data.status) {
      return true; // Si no es cuenta gratuita, no hay límites
    }

    if (!data.status.isActive) {
      return false; // Cuenta expirada
    }

    const currentUsage = data.status.usage[feature as keyof typeof data.status.usage] || 0;
    const limit = data.status.limits[feature as keyof typeof data.status.limits] || 0;

    return currentUsage + amount <= limit;
  }, [data]);

  const refresh = useCallback(async () => {
    await fetchStatus();
  }, [fetchStatus]);

  useEffect(() => {
    fetchStatus();
  }, [fetchStatus]);

  return {
    ...data,
    refresh,
    canPerformAction
  };
}

/**
 * Hook específico para validar acciones antes de ejecutarlas
 */
export function useFreeAccountValidation() {
  const { isFreeAccount, status, canPerformAction } = useFreeAccount();

  const validateAndExecute = useCallback(async <T>(
    feature: string,
    amount: number = 1,
    action: () => Promise<T>
  ): Promise<{ success: boolean; result?: T; error?: string }> => {
    
    // Si no es cuenta gratuita, ejecutar directamente
    if (!isFreeAccount) {
      try {
        const result = await action();
        return { success: true, result };
      } catch (error) {
        return { 
          success: false, 
          error: error instanceof Error ? error.message : 'Error desconocido' 
        };
      }
    }

    // Validar límites para cuenta gratuita
    if (!canPerformAction(feature, amount)) {
      const currentUsage = status?.usage[feature as keyof typeof status.usage] || 0;
      const limit = status?.limits[feature as keyof typeof status.limits] || 0;
      
      if (!status?.isActive) {
        return { 
          success: false, 
          error: 'Tu cuenta gratuita ha expirado. Actualiza tu plan para continuar.' 
        };
      }
      
      return { 
        success: false, 
        error: `Has alcanzado el límite de ${feature} (${currentUsage}/${limit}). Actualiza tu plan para continuar.` 
      };
    }

    try {
      // Ejecutar acción
      const result = await action();

      // NOTA: El incremento de uso ahora se hace automáticamente en el backend (api/ai/route.ts)
      // después de que la generación de IA sea exitosa, por lo que no necesitamos hacerlo aquí

      return { success: true, result };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      };
    }
  }, [isFreeAccount, status, canPerformAction]);

  return {
    isFreeAccount,
    status,
    validateAndExecute,
    canPerformAction
  };
}

/**
 * Hook para mostrar alertas de expiración
 */
export function useFreeAccountAlerts() {
  const { isFreeAccount, status, alerts, usageWarnings } = useFreeAccount();
  const [dismissed, setDismissed] = useState<Set<string>>(new Set());

  const visibleAlerts = alerts.filter(alert => 
    !dismissed.has(`${alert.type}-${alert.message}`)
  );

  const visibleWarnings = usageWarnings.filter(warning => 
    !dismissed.has(`${warning.feature}-${warning.severity}`)
  );

  const dismissAlert = useCallback((alert: FreeAccountAlert) => {
    setDismissed(prev => new Set(prev).add(`${alert.type}-${alert.message}`));
  }, []);

  const dismissWarning = useCallback((warning: UsageWarning) => {
    setDismissed(prev => new Set(prev).add(`${warning.feature}-${warning.severity}`));
  }, []);

  const hasActiveAlerts = visibleAlerts.length > 0 || visibleWarnings.length > 0;

  const shouldShowUpgradePrompt = isFreeAccount && status && (
    !status.isActive || 
    status.daysRemaining <= 1 ||
    Object.values(status.usagePercentages).some(percentage => percentage >= 80)
  );

  return {
    isFreeAccount,
    status,
    visibleAlerts,
    visibleWarnings,
    hasActiveAlerts,
    shouldShowUpgradePrompt,
    dismissAlert,
    dismissWarning
  };
}

/**
 * Hook para formatear tiempo restante
 */
export function useTimeRemaining(expiresAt: string | null) {
  const [timeRemaining, setTimeRemaining] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    isExpired: boolean;
    formatted: string;
  }>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    isExpired: true,
    formatted: 'Expirado'
  });

  useEffect(() => {
    if (!expiresAt) {
      return;
    }

    const updateTime = () => {
      const now = new Date().getTime();
      const expiry = new Date(expiresAt).getTime();
      const diff = expiry - now;

      if (diff <= 0) {
        setTimeRemaining({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          isExpired: true,
          formatted: 'Expirado'
        });
        return;
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      let formatted = '';
      if (days > 0) {
        formatted = `${days}d ${hours}h`;
      } else if (hours > 0) {
        formatted = `${hours}h ${minutes}m`;
      } else if (minutes > 0) {
        formatted = `${minutes}m ${seconds}s`;
      } else {
        formatted = `${seconds}s`;
      }

      setTimeRemaining({
        days,
        hours,
        minutes,
        seconds,
        isExpired: false,
        formatted
      });
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, [expiresAt]);

  return timeRemaining;
}
