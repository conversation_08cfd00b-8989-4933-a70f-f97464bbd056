import { createServerSupabaseClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import AdminSidebar from '@/features/admin/components/AdminSidebar';

// Lista de administradores autorizados
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Añadir más emails si es necesario
];

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createServerSupabaseClient();
  const { data: { user } } = await supabase.auth.getUser();

  // Redirigir si no es un administrador
  if (!user || !user.email || !ADMIN_EMAILS.includes(user.email)) {
    redirect('/login');
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="flex">
        <AdminSidebar />
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
