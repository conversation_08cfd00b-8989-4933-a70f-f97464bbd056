import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import * as Sentry from "@sentry/nextjs";

// ¡IMPORTANTE! Protege este endpoint con una clave secreta.
const CRON_SECRET = process.env.CRON_SECRET;
const WORKER_URL = `${process.env.NEXT_PUBLIC_APP_URL}/api/tasks/process-ai-job`;

export async function GET(request: NextRequest) {
  try {
    // 1. Proteger el endpoint
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${CRON_SECRET}`) {
      console.warn('🚫 [DISPATCHER] Intento de acceso no autorizado');
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔄 [DISPATCHER] Iniciando búsqueda de tareas pendientes...');

    // 2. <PERSON>car tareas pendientes
    const { data: pendingTasks, error } = await supabaseAdmin
      .from('ai_tasks')
      .select('id, type, created_at')
      .eq('status', 'pending')
      .order('created_at', { ascending: true }) // Procesar las más antiguas primero
      .limit(5); // Procesar hasta 5 tareas por minuto para no sobrecargar

    if (error) {
      console.error('❌ [DISPATCHER] Error buscando tareas:', error);
      Sentry.captureException(error, {
        tags: { section: "task-dispatcher" },
        extra: {
          context: "Error searching for pending tasks",
          timestamp: new Date().toISOString()
        },
      });
      return NextResponse.json({ error: 'Error buscando tareas' }, { status: 500 });
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log('✅ [DISPATCHER] No hay tareas pendientes.');
      return NextResponse.json({ 
        message: 'No hay tareas pendientes.',
        dispatched: 0,
        timestamp: new Date().toISOString()
      });
    }

    console.log(`📋 [DISPATCHER] Encontradas ${pendingTasks.length} tareas pendientes`);

    // 3. Disparar un worker para cada tarea pendiente (sin esperar la respuesta)
    const dispatchPromises = pendingTasks.map(async (task) => {
      try {
        console.log(`🚀 [DISPATCHER] Disparando worker para tarea ${task.id} (${task.type})`);
        
        const response = await fetch(WORKER_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${CRON_SECRET}`
          },
          body: JSON.stringify({ taskId: task.id }),
        });

        if (!response.ok) {
          throw new Error(`Worker respondió con status ${response.status}`);
        }

        console.log(`✅ [DISPATCHER] Worker disparado exitosamente para tarea ${task.id}`);
        return { taskId: task.id, success: true };
      } catch (error) {
        console.error(`❌ [DISPATCHER] Error disparando worker para tarea ${task.id}:`, error);
        
        // Capturar error en Sentry pero no fallar todo el proceso
        Sentry.captureException(error, {
          tags: { section: "task-dispatcher" },
          extra: {
            context: "Error dispatching worker",
            taskId: task.id,
            taskType: task.type,
            timestamp: new Date().toISOString()
          },
        });

        return { taskId: task.id, success: false, error: error instanceof Error ? error.message : 'Error desconocido' };
      }
    });

    // 4. Esperar a que todos los dispatches se completen
    const results = await Promise.allSettled(dispatchPromises);
    
    const successful = results.filter(result => 
      result.status === 'fulfilled' && result.value.success
    ).length;
    
    const failed = results.length - successful;

    console.log(`📊 [DISPATCHER] Resumen: ${successful} exitosos, ${failed} fallidos`);

    return NextResponse.json({ 
      success: true, 
      dispatched: successful,
      failed: failed,
      total: pendingTasks.length,
      timestamp: new Date().toISOString(),
      tasks: pendingTasks.map(task => ({
        id: task.id,
        type: task.type,
        age: Math.round((Date.now() - new Date(task.created_at).getTime()) / 1000) // edad en segundos
      }))
    });

  } catch (error) {
    console.error('❌ [DISPATCHER] Error crítico:', error);
    
    Sentry.captureException(error, {
      tags: { section: "task-dispatcher" },
      extra: {
        context: "Critical error in task dispatcher",
        timestamp: new Date().toISOString()
      },
    });

    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
