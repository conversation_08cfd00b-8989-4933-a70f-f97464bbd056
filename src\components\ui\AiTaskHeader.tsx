'use client';

import React from 'react';
import { FiCheck, FiAlertTriangle } from 'react-icons/fi';
import { Documento } from '@/lib/supabase/supabaseClient';

interface AiTaskHeaderProps {
  icon: React.ReactNode;
  title: string;
  documentoSeleccionado: Documento | null;
}

const formatFileSize = (bytes?: number): string => {
  if (!bytes) return 'Tamaño desconocido';
  
  if (bytes < 1024) {
    return `${bytes} bytes`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
};

export default function AiTaskHeader({ icon, title, documentoSeleccionado }: AiTaskHeaderProps) {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
      <div className="flex items-center mb-3">
        <div className="flex items-center text-blue-900">
          {icon}
          <h3 className="font-semibold ml-2">{title}</h3>
        </div>
      </div>

      {documentoSeleccionado ? (
        <div className="text-blue-700">
          <p className="font-medium flex items-center">
            <FiCheck className="w-4 h-4 mr-2 text-green-600" />
            Documento seleccionado:
          </p>
          <p className="text-sm mt-1 ml-6">
            <strong>{documentoSeleccionado.titulo}</strong>
            {documentoSeleccionado.numero_tema && ` (Tema ${documentoSeleccionado.numero_tema})`}
          </p>
          <p className="text-xs mt-1 ml-6 text-blue-600">
            {documentoSeleccionado.storage_path && documentoSeleccionado.file_size_bytes
              ? `Tamaño: ${formatFileSize(documentoSeleccionado.file_size_bytes)}`
              : `Contenido: ~${(documentoSeleccionado.contenido_corto || documentoSeleccionado.contenido || "").split(/\s+/).length} palabras`
            }
          </p>
          <p className="text-xs mt-2 ml-6 text-blue-500 italic">
            💡 Trabajar con un solo documento permite generar contenido más preciso y de mayor calidad.
          </p>
        </div>
      ) : (
        <div className="text-red-700">
          <p className="font-medium flex items-center">
            <FiAlertTriangle className="w-4 h-4 mr-2" />
            Selección requerida
          </p>
          <p className="text-sm mt-1 ml-6">
            Debes seleccionar un documento para utilizar esta función.
          </p>
          <p className="text-xs mt-2 ml-6 text-red-500 italic">
            💡 Selecciona un documento para obtener respuestas más precisas y enfocadas.
          </p>
        </div>
      )}
    </div>
  );
}
