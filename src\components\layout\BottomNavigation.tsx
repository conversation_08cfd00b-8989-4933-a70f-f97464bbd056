'use client';

import React from 'react';
import { TabType } from '@/types/ui';

interface BottomNavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  className?: string;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  activeTab,
  onTabChange,
  className = ''
}) => {
  const quickAccessTabs = [
    {
      id: 'dashboard' as TabType,
      label: 'Inicio',
      icon: '🏠'
    },
    {
      id: 'preguntas' as TabType,
      label: 'Chat',
      icon: '💬'
    },
    {
      id: 'misFlashcards' as TabType,
      label: 'Flashcards',
      icon: '📚'
    },
    {
      id: 'misTests' as TabType,
      label: 'Tests',
      icon: '📝'
    }
  ];

  return (
    <div className={`fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-40 lg:hidden ${className}`}>
      <div className="flex justify-around items-center max-w-md mx-auto">
        {quickAccessTabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`flex flex-col items-center justify-center py-2 px-3 rounded-lg transition-colors min-w-0 ${
              activeTab === tab.id
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <span className="text-lg mb-1">{tab.icon}</span>
            <span className="text-xs font-medium truncate">{tab.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default BottomNavigation;
