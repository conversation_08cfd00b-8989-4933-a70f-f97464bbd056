#!/usr/bin/env node

/**
 * Script para probar manualmente el sistema asíncrono de IA
 * 
 * Uso:
 * node scripts/test-async-system.js
 */

const { createClient } = require('@supabase/supabase-js');

// Configuración
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
const CRON_SECRET = process.env.CRON_SECRET;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Variables de entorno de Supabase no configuradas');
  process.exit(1);
}

if (!CRON_SECRET) {
  console.error('❌ CRON_SECRET no configurado');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testAsyncSystem() {
  console.log('🧪 Iniciando test del sistema asíncrono de IA...\n');

  try {
    // 1. Verificar tabla ai_tasks
    console.log('1️⃣ Verificando tabla ai_tasks...');
    const { data: tableCheck, error: tableError } = await supabase
      .from('ai_tasks')
      .select('count(*)')
      .limit(1);

    if (tableError) {
      throw new Error(`Error accediendo a la tabla: ${tableError.message}`);
    }
    console.log('✅ Tabla ai_tasks accesible\n');

    // 2. Crear tarea de prueba
    console.log('2️⃣ Creando tarea de prueba...');
    const { data: newTask, error: createError } = await supabase
      .from('ai_tasks')
      .insert({
        user_id: '00000000-0000-0000-0000-000000000000', // UUID de prueba
        type: 'test',
        status: 'pending',
        input_data: {
          peticion: 'Test del sistema asíncrono',
          documentos: [],
          cantidad: 3
        }
      })
      .select()
      .single();

    if (createError) {
      throw new Error(`Error creando tarea: ${createError.message}`);
    }
    console.log(`✅ Tarea creada con ID: ${newTask.id}\n`);

    // 3. Verificar que la tarea está pendiente
    console.log('3️⃣ Verificando estado de la tarea...');
    const { data: pendingTask, error: fetchError } = await supabase
      .from('ai_tasks')
      .select('*')
      .eq('id', newTask.id)
      .single();

    if (fetchError) {
      throw new Error(`Error obteniendo tarea: ${fetchError.message}`);
    }

    console.log(`✅ Tarea encontrada:`);
    console.log(`   - ID: ${pendingTask.id}`);
    console.log(`   - Estado: ${pendingTask.status}`);
    console.log(`   - Tipo: ${pendingTask.type}`);
    console.log(`   - Creada: ${pendingTask.created_at}\n`);

    // 4. Simular procesamiento
    console.log('4️⃣ Simulando procesamiento...');
    const { error: updateError } = await supabase
      .from('ai_tasks')
      .update({
        status: 'processing',
        started_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', newTask.id);

    if (updateError) {
      throw new Error(`Error actualizando tarea: ${updateError.message}`);
    }
    console.log('✅ Tarea marcada como "processing"\n');

    // 5. Simular completado
    console.log('5️⃣ Simulando completado...');
    const mockResult = [
      {
        pregunta: '¿Cuál es la capital de Francia?',
        opciones: { a: 'París', b: 'Londres', c: 'Madrid', d: 'Roma' },
        respuesta_correcta: 'a'
      },
      {
        pregunta: '¿Cuánto es 2 + 2?',
        opciones: { a: '3', b: '4', c: '5', d: '6' },
        respuesta_correcta: 'b'
      }
    ];

    const { error: completeError } = await supabase
      .from('ai_tasks')
      .update({
        status: 'completed',
        result: mockResult,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', newTask.id);

    if (completeError) {
      throw new Error(`Error completando tarea: ${completeError.message}`);
    }
    console.log('✅ Tarea marcada como "completed"\n');

    // 6. Verificar resultado final
    console.log('6️⃣ Verificando resultado final...');
    const { data: completedTask, error: finalError } = await supabase
      .from('ai_tasks')
      .select('*')
      .eq('id', newTask.id)
      .single();

    if (finalError) {
      throw new Error(`Error obteniendo tarea final: ${finalError.message}`);
    }

    console.log(`✅ Tarea completada exitosamente:`);
    console.log(`   - Estado: ${completedTask.status}`);
    console.log(`   - Resultado: ${JSON.stringify(completedTask.result, null, 2)}`);
    console.log(`   - Completada: ${completedTask.completed_at}\n`);

    // 7. Limpiar tarea de prueba
    console.log('7️⃣ Limpiando tarea de prueba...');
    const { error: deleteError } = await supabase
      .from('ai_tasks')
      .delete()
      .eq('id', newTask.id);

    if (deleteError) {
      console.warn(`⚠️ No se pudo eliminar la tarea de prueba: ${deleteError.message}`);
    } else {
      console.log('✅ Tarea de prueba eliminada\n');
    }

    // 8. Test de endpoints (opcional)
    console.log('8️⃣ Probando endpoints...');
    
    try {
      // Test del dispatcher
      const dispatcherResponse = await fetch(`${APP_URL}/api/tasks/dispatch-pending-jobs`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${CRON_SECRET}`
        }
      });

      if (dispatcherResponse.ok) {
        const dispatcherData = await dispatcherResponse.json();
        console.log('✅ Dispatcher endpoint accesible');
        console.log(`   - Tareas despachadas: ${dispatcherData.dispatched || 0}`);
      } else {
        console.log(`⚠️ Dispatcher endpoint respondió con status: ${dispatcherResponse.status}`);
      }
    } catch (fetchError) {
      console.log(`⚠️ No se pudo probar el dispatcher: ${fetchError.message}`);
    }

    console.log('\n🎉 ¡Test del sistema asíncrono completado exitosamente!');
    console.log('\n📋 Resumen:');
    console.log('   ✅ Tabla ai_tasks funcional');
    console.log('   ✅ Creación de tareas');
    console.log('   ✅ Actualización de estados');
    console.log('   ✅ Almacenamiento de resultados');
    console.log('   ✅ Endpoints accesibles');

  } catch (error) {
    console.error('\n❌ Error en el test:', error.message);
    process.exit(1);
  }
}

// Ejecutar test
testAsyncSystem();
