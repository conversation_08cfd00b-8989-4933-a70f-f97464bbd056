/**
 * Convierte contenido markdown a HTML formateado para PDF
 */
export function markdownToHTML(
  content: string,
  title?: string,
  metadata?: {
    createdAt?: string;
    instructions?: string;
    author?: string;
  }
): string {
  // Normalizar saltos de línea
  content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // Dividir en párrafos por dobles saltos de línea
  const paragraphs = content.split(/\n\s*\n/);

  let html = '';

  for (let paragraph of paragraphs) {
    paragraph = paragraph.trim();
    if (!paragraph) continue;

    // Procesar cada párrafo
    let processedParagraph = paragraph
      // Encabezados
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')

      // Texto en negrita y cursiva
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')

      // Citas
      .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')

      // Listas
      .replace(/^\- (.*$)/gim, '<li>$1</li>')
      .replace(/^\* (.*$)/gim, '<li>$1</li>')
      .replace(/^\+ (.*$)/gim, '<li>$1</li>')

      // Saltos de línea simples dentro del párrafo
      .replace(/\n/g, ' ');

    // Envolver listas en elementos ul
    if (processedParagraph.includes('<li>')) {
      processedParagraph = processedParagraph.replace(/(<li>.*?<\/li>)/g, (match) => {
        return `<ul>${match}</ul>`;
      });
      html += processedParagraph;
    }
    // Si es un encabezado o blockquote, no envolver en <p>
    else if (processedParagraph.startsWith('<h1>') ||
             processedParagraph.startsWith('<h2>') ||
             processedParagraph.startsWith('<h3>') ||
             processedParagraph.startsWith('<blockquote>')) {
      html += processedParagraph;
    }
    // Envolver texto normal en párrafos
    else {
      html += `<p>${processedParagraph}</p>`;
    }
  }

  // Construir el HTML completo
  let fullHTML = '';

  // Agregar título si existe
  if (title) {
    fullHTML += `<h1>${title}</h1>`;
  }

  // Agregar metadatos si existen
  if (metadata) {
    fullHTML += '<div class="metadata">';
    if (metadata.createdAt) {
      const fecha = new Date(metadata.createdAt).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      fullHTML += `<p><strong>Fecha de creación:</strong> ${fecha}</p>`;
    }
    if (metadata.author) {
      fullHTML += `<p><strong>Autor:</strong> ${metadata.author}</p>`;
    }
    if (metadata.instructions) {
      fullHTML += `<p><strong>Instrucciones utilizadas:</strong> ${metadata.instructions}</p>`;
    }
    fullHTML += '</div>';
  }

  // Agregar el contenido principal
  fullHTML += html;

  return fullHTML;
}

/**
 * Convierte contenido markdown a texto plano para PDF simple
 */
export function markdownToPlainText(
  content: string,
  title?: string,
  metadata?: {
    createdAt?: string;
    instructions?: string;
    author?: string;
  }
): string {
  // Convertir markdown a texto plano
  let text = content
    // Remover marcas de markdown
    .replace(/^#{1,6}\s+/gm, '') // Encabezados
    .replace(/\*\*(.*?)\*\*/g, '$1') // Negrita
    .replace(/\*(.*?)\*/g, '$1') // Cursiva
    .replace(/^>\s+/gm, '') // Citas
    .replace(/^[-*+]\s+/gm, '• ') // Listas
    .replace(/\n{3,}/g, '\n\n'); // Múltiples saltos de línea

  // Construir el texto completo
  let fullText = '';

  // Agregar título si existe
  if (title) {
    fullText += `${title}\n`;
    fullText += '='.repeat(title.length) + '\n\n';
  }

  // Agregar metadatos si existen
  if (metadata) {
    if (metadata.createdAt) {
      const fecha = new Date(metadata.createdAt).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      fullText += `Fecha de creación: ${fecha}\n`;
    }
    if (metadata.author) {
      fullText += `Autor: ${metadata.author}\n`;
    }
    if (metadata.instructions) {
      fullText += `Instrucciones utilizadas: ${metadata.instructions}\n`;
    }
    fullText += '\n';
  }

  // Agregar el contenido principal
  fullText += text;

  return fullText;
}
