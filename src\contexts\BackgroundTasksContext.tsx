'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode, useMemo, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { taskPersistenceService } from '@/lib/services/TaskPersistenceService';
import { createClient } from '@/lib/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

// Función para mapear datos de la base de datos al formato de UI
function dbTaskToUiTask(dbTask: any): BackgroundTask {
  return {
    id: dbTask.id,
    type: dbTask.type,
    title: dbTask.input_data?.peticion || 'Tarea de IA',
    status: dbTask.status,
    result: dbTask.result,
    error: dbTask.error_message,
    createdAt: new Date(dbTask.created_at),
    completedAt: dbTask.completed_at ? new Date(dbTask.completed_at) : undefined
  };
}

export interface BackgroundTask {
  id: string;
  type: 'mapa-mental' | 'test' | 'flashcards' | 'plan-estudios' | 'resumen';
  title: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress?: number;
  result?: any;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

interface BackgroundTasksContextType {
  tasks: BackgroundTask[];
  addTask: (task: Omit<BackgroundTask, 'id' | 'status' | 'createdAt'>) => string;
  updateTask: (id: string, updates: Partial<BackgroundTask>) => void;
  removeTask: (id: string) => void;
  getTask: (id: string) => BackgroundTask | undefined;
  getTasksByType: (type: BackgroundTask['type']) => BackgroundTask[];
  clearCompletedTasks: () => void;
  clearAllTasks: () => void; // Nueva función para limpiar todas las tareas
  activeTasks: BackgroundTask[];
  completedTasks: BackgroundTask[];
  isInitialized: boolean;
  getTaskStats: () => ReturnType<typeof taskPersistenceService.getTaskStats>;
}

const BackgroundTasksContext = createContext<BackgroundTasksContextType | undefined>(undefined);

interface BackgroundTasksProviderProps {
  children: ReactNode;
}

export const BackgroundTasksProvider: React.FC<BackgroundTasksProviderProps> = ({ children }) => {
  const [tasks, setTasks] = useState<BackgroundTask[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const { user } = useAuth(); // Necesitamos el usuario para las consultas
  const supabase = createClient();

  // Cargar tareas iniciales desde la DB y suscribirse a cambios
  useEffect(() => {
    if (!user) {
      setIsInitialized(true);
      return;
    }

    const fetchInitialTasks = async () => {
      try {
        console.log('🔄 [BACKGROUND_TASKS] Cargando tareas iniciales desde Supabase...');

        // Cargar tareas no completadas desde la base de datos
        const { data, error } = await supabase
          .from('ai_tasks')
          .select('*')
          .in('status', ['pending', 'processing'])
          .order('created_at', { ascending: false });

        if (error) {
          console.error('❌ [BACKGROUND_TASKS] Error cargando tareas:', error);
          // Fallback a localStorage si hay error con Supabase
          const persistedTasks = taskPersistenceService.loadTasks();
          if (persistedTasks.length > 0) {
            setTasks(persistedTasks);
          }
        } else if (data) {
          console.log(`✅ [BACKGROUND_TASKS] Cargadas ${data.length} tareas desde Supabase`);
          setTasks(data.map(dbTaskToUiTask));
        }
      } catch (error) {
        console.error('❌ [BACKGROUND_TASKS] Error al cargar tareas:', error);
        // Fallback a localStorage
        const persistedTasks = taskPersistenceService.loadTasks();
        if (persistedTasks.length > 0) {
          setTasks(persistedTasks);
        }
      } finally {
        setIsInitialized(true);
      }
    };

    fetchInitialTasks();

    // Escuchar cambios en tiempo real
    console.log('🔔 [BACKGROUND_TASKS] Configurando suscripción Realtime...');
    const channel = supabase
      .channel('ai-tasks-channel')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'ai_tasks',
        filter: `user_id=eq.${user.id}` // Solo escuchar cambios del usuario actual
      }, (payload) => {
        console.log('📡 [BACKGROUND_TASKS] Cambio recibido via Realtime:', payload);

        if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
          const updatedTask = dbTaskToUiTask(payload.new);
          setTasks(prev => {
            const existingIndex = prev.findIndex(t => t.id === updatedTask.id);
            if (existingIndex > -1) {
              // Actualizar tarea existente
              const newTasks = [...prev];
              newTasks[existingIndex] = updatedTask;
              return newTasks;
            } else {
              // Añadir nueva tarea
              return [...prev, updatedTask];
            }
          });
        } else if (payload.eventType === 'DELETE') {
          setTasks(prev => prev.filter(t => t.id !== payload.old.id));
        }
      })
      .subscribe();

    return () => {
      console.log('🔌 [BACKGROUND_TASKS] Desconectando suscripción Realtime...');
      supabase.removeChannel(channel);
    };
  }, [user, supabase]);

  // Mantener persistencia local como backup (solo después de la inicialización)
  useEffect(() => {
    if (isInitialized) {
      taskPersistenceService.saveTasks(tasks);
    }
  }, [tasks, isInitialized]);

  const addTask = useCallback((taskData: Omit<BackgroundTask, 'id' | 'status' | 'createdAt'>) => {
    const id = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newTask: BackgroundTask = {
      ...taskData,
      id,
      status: 'pending',
      createdAt: new Date(),
    };

    setTasks(prev => [...prev, newTask]);

    // Mostrar notificación de inicio
    toast.loading(`Iniciando: ${newTask.title}`, {
      id: `task_start_${id}`,
      duration: 2000,
    });

    return id;
  }, []);

  const updateTask = useCallback((id: string, updates: Partial<BackgroundTask>) => {
    setTasks(prev => prev.map(task => {
      if (task.id === id) {
        const updatedTask = { ...task, ...updates };

        // Manejar notificaciones según el estado
        if (updates.status === 'processing' && task.status === 'pending') {
          toast.dismiss(`task_start_${id}`);
          toast.loading(`Procesando: ${task.title}`, {
            id: `task_processing_${id}`,
          });
        } else if (updates.status === 'completed' && task.status !== 'completed') {
          toast.dismiss(`task_processing_${id}`);
          toast.success(`Completado: ${task.title}`, {
            id: `task_completed_${id}`,
            duration: 4000,
          });
          updatedTask.completedAt = new Date();
        } else if (updates.status === 'error' && task.status !== 'error') {
          toast.dismiss(`task_processing_${id}`);
          toast.error(`Error: ${task.title}`, {
            id: `task_error_${id}`,
            duration: 5000,
          });
        }

        return updatedTask;
      }
      return task;
    }));
  }, []);

  const removeTask = useCallback((id: string) => {
    setTasks(prev => prev.filter(task => task.id !== id));
    // Limpiar notificaciones relacionadas
    toast.dismiss(`task_start_${id}`);
    toast.dismiss(`task_processing_${id}`);
    toast.dismiss(`task_completed_${id}`);
    toast.dismiss(`task_error_${id}`);
  }, []);

  const getTask = useCallback((id: string) => {
    return tasks.find(task => task.id === id);
  }, [tasks]);

  const getTasksByType = useCallback((type: BackgroundTask['type']) => {
    return tasks.filter(task => task.type === type);
  }, [tasks]);

  const clearCompletedTasks = useCallback(() => {
    setTasks(prev => {
      const activeTasks = prev.filter(task => task.status !== 'completed' && task.status !== 'error');
      return activeTasks;
    });
  }, []);

  const clearAllTasks = useCallback(() => {
    setTasks([]);
    taskPersistenceService.clearTasks();
    // Limpiar todas las notificaciones de tareas
    toast.dismiss();
  }, []);

  const activeTasks = useMemo(() => tasks.filter(task =>
    task.status === 'pending' || task.status === 'processing'
  ), [tasks]);

  const completedTasks = useMemo(() => tasks.filter(task =>
    task.status === 'completed' || task.status === 'error'
  ), [tasks]);

  const getTaskStats = useCallback(() => {
    return taskPersistenceService.getTaskStats();
  }, []);

  const value: BackgroundTasksContextType = useMemo(() => ({
    tasks,
    addTask,
    updateTask,
    removeTask,
    getTask,
    getTasksByType,
    clearCompletedTasks,
    clearAllTasks,
    activeTasks,
    completedTasks,
    isInitialized,
    getTaskStats,
  }), [tasks, addTask, updateTask, removeTask, getTask, getTasksByType, clearCompletedTasks, clearAllTasks, activeTasks, completedTasks, isInitialized, getTaskStats]);

  return (
    <BackgroundTasksContext.Provider value={value}>
      {children}
    </BackgroundTasksContext.Provider>
  );
};

export const useBackgroundTasks = () => {
  const context = useContext(BackgroundTasksContext);
  if (context === undefined) {
    throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');
  }
  return context;
};
