import {
  dividirEnChunks,
  obtenerEstadisticasChunking,
  type Chunk,
  type ChunkingConfig
} from '@/lib/utils/textProcessing';

import {
  type DocumentChunkingResult,
  type DocumentProcessingOptions,
  type ChunkingStats,
  type ChunkingProgress,
  ChunkingError,
  ChunkingErrorCode
} from '@/types/chunking';

import {
  getChunkingConfigForContentType,
  validateChunkingConfig,
  CHUNKING_LIMITS,
  CHUNKING_LOGGING_CONFIG
} from '@/config/chunking';

import { chunkingPerformanceMonitor } from './ChunkingPerformanceMonitor';
import { type Documento } from '@/types/database';

/**
 * Obtiene el contenido de un documento desde Storage o fallback a contenido en BD
 * @param documento Documento que puede tener storage_path o contenido
 * @returns El contenido del documento como string
 */
async function obtenerContenidoDocumento(documento: Documento): Promise<string> {
  try {
    // Prioridad 1: Descargar desde Storage si existe storage_path
    if (documento.storage_path) {
      const { downloadFileContentFromServer } = await import('../supabase/storageService.server');
      const contenidoStorage = await downloadFileContentFromServer(documento.storage_path);
      if (contenidoStorage) {
        return contenidoStorage;
      }
      // Si la descarga falla, lanzar error en lugar de fallback silencioso
      console.error('❌ [CHUNKING] Fallo crítico al descargar desde Storage:', {
        docId: documento.id,
        path: documento.storage_path,
        titulo: documento.titulo
      });
      throw new Error(`No se pudo descargar el documento "${documento.titulo || 'Sin título'}" desde Storage.`);
    }

    // Prioridad 2: Usar contenido_corto si existe (enfoque híbrido)
    if (documento.contenido_corto) {
      return documento.contenido_corto;
    }

    // Prioridad 3: Usar contenido tradicional si existe (compatibilidad)
    if (documento.contenido) {
      return documento.contenido;
    }

    console.warn('⚠️ [CHUNKING] Documento sin contenido disponible:', documento.id);
    throw new Error(`Documento ${documento.id} no tiene contenido disponible`);
  } catch (error) {
    console.error('❌ [CHUNKING] Error al obtener contenido del documento:', {
      docId: documento.id,
      error: error instanceof Error ? error.message : 'Error desconocido'
    });
    throw error;
  }
}

/**
 * Servicio principal para el chunking de documentos
 */
export class DocumentChunkingService {
  private static readonly logger = CHUNKING_LOGGING_CONFIG.customLogger || console.log;

  /**
   * Procesa un documento de forma asíncrona, obteniendo contenido desde Storage si es necesario
   *
   * @param documento - Documento a procesar (puede tener storage_path)
   * @param options - Opciones de procesamiento
   * @returns Resultado del procesamiento (string simple o array de chunks)
   */
  static async processDocumentAsync(
    documento: Documento,
    options: DocumentProcessingOptions = { enableChunking: true }
  ): Promise<DocumentChunkingResult> {
    const startTime = Date.now();

    try {
      // Obtener contenido del documento (desde Storage o fallback)
      const contenido = await obtenerContenidoDocumento(documento);

      // Crear documento con contenido para procesamiento síncrono
      const documentoConContenido: Documento = {
        ...documento,
        contenido
      };

      // Validar entrada
      this.validateInput(documentoConContenido, options);

      // Obtener configuración de chunking
      const config = this.getEffectiveConfig(options);

      // Determinar si necesita chunking
      const needsChunking = this.shouldChunkDocument(documentoConContenido, options, config);

      if (!needsChunking) {
        return this.createSimpleResult(documentoConContenido, config, startTime);
      }

      // Realizar chunking
      return this.performChunking(documentoConContenido, config, options, startTime);

    } catch (error) {
      this.logger('error', 'Error en processDocumentAsync', {
        documentId: documento.id,
        titulo: documento.titulo,
        error: error instanceof Error ? error.message : String(error)
      });

      if (error instanceof ChunkingError) {
        throw error;
      }

      throw new ChunkingError(
        `Error procesando documento: ${error instanceof Error ? error.message : String(error)}`,
        ChunkingErrorCode.UNKNOWN_ERROR,
        { documentId: documento.id, originalError: error }
      );
    }
  }

  /**
   * Procesa un documento y determina si necesita chunking (versión síncrona original)
   *
   * @param documento - Documento a procesar (debe tener contenido)
   * @param options - Opciones de procesamiento
   * @returns Resultado del procesamiento (string simple o array de chunks)
   */
  static processDocument(
    documento: Documento,
    options: DocumentProcessingOptions = { enableChunking: true }
  ): DocumentChunkingResult {
    const startTime = Date.now();

    try {
      // Validar entrada
      this.validateInput(documento, options);

      // Obtener configuración de chunking
      const config = this.getEffectiveConfig(options);

      // Determinar si necesita chunking
      const needsChunking = this.shouldChunkDocument(documento, options, config);

      if (!needsChunking) {
        return this.createSimpleResult(documento, config, startTime);
      }

      // Realizar chunking
      return this.performChunking(documento, config, options, startTime);
      
    } catch (error) {
      this.logger('error', 'Error en processDocument', { 
        documentId: documento.id,
        error: error instanceof Error ? error.message : String(error)
      });
      
      if (error instanceof ChunkingError) {
        throw error;
      }
      
      throw new ChunkingError(
        `Error inesperado al procesar documento: ${error instanceof Error ? error.message : String(error)}`,
        ChunkingErrorCode.UNKNOWN_ERROR,
        { originalError: error }
      );
    }
  }

  /**
   * Procesa múltiples documentos de forma asíncrona
   */
  static async processMultipleDocumentsAsync(
    documentos: Documento[],
    options: DocumentProcessingOptions = { enableChunking: true }
  ): Promise<DocumentChunkingResult[]> {
    this.logger('info', `Procesando ${documentos.length} documentos de forma asíncrona`);

    const resultados = await Promise.allSettled(
      documentos.map(documento => this.processDocumentAsync(documento, options))
    );

    return resultados.map((resultado, index) => {
      if (resultado.status === 'fulfilled') {
        return resultado.value;
      } else {
        const documento = documentos[index];
        this.logger('error', 'Error procesando documento async', {
          documentId: documento.id,
          titulo: documento.titulo,
          error: resultado.reason instanceof Error ? resultado.reason.message : String(resultado.reason)
        });

        // Retornar resultado de fallback para este documento
        return this.createErrorResult(documento, resultado.reason);
      }
    });
  }

  /**
   * Procesa múltiples documentos (versión síncrona original)
   */
  static processMultipleDocuments(
    documentos: Documento[],
    options: DocumentProcessingOptions = { enableChunking: true }
  ): DocumentChunkingResult[] {
    this.logger('info', `Procesando ${documentos.length} documentos`);
    
    return documentos.map(documento => {
      try {
        return this.processDocument(documento, options);
      } catch (error) {
        this.logger('error', 'Error procesando documento', {
          documentId: documento.id,
          titulo: documento.titulo,
          error: error instanceof Error ? error.message : String(error)
        });
        
        // Retornar resultado de fallback para este documento
        return this.createErrorResult(documento, error);
      }
    });
  }

  /**
   * Valida la entrada del servicio
   */
  private static validateInput(documento: Documento, options: DocumentProcessingOptions): void {
    if (!documento) {
      throw new ChunkingError(
        'Documento no proporcionado',
        ChunkingErrorCode.INVALID_CONTENT
      );
    }

    if (!documento.contenido || typeof documento.contenido !== 'string') {
      throw new ChunkingError(
        'El documento debe tener contenido válido',
        ChunkingErrorCode.INVALID_CONTENT,
        { documentId: documento.id, titulo: documento.titulo }
      );
    }

    if (documento.contenido.length > CHUNKING_LIMITS.MAX_DOCUMENT_SIZE) {
      throw new ChunkingError(
        `Documento demasiado grande: ${documento.contenido.length} caracteres (máximo: ${CHUNKING_LIMITS.MAX_DOCUMENT_SIZE})`,
        ChunkingErrorCode.CHUNK_TOO_LARGE,
        { documentId: documento.id, size: documento.contenido.length }
      );
    }

    // Validar configuración personalizada si se proporciona
    if (options.chunkingConfig) {
      const validation = validateChunkingConfig(options.chunkingConfig);
      if (!validation.isValid) {
        throw new ChunkingError(
          `Configuración de chunking inválida: ${validation.errors.join(', ')}`,
          ChunkingErrorCode.CONFIG_ERROR,
          { errors: validation.errors, warnings: validation.warnings }
        );
      }
    }
  }

  /**
   * Obtiene la configuración efectiva de chunking
   */
  private static getEffectiveConfig(options: DocumentProcessingOptions): ChunkingConfig {
    return getChunkingConfigForContentType(
      options.contentType,
      options.chunkingConfig
    );
  }

  /**
   * Determina si un documento necesita chunking
   */
  private static shouldChunkDocument(
    documento: Documento,
    options: DocumentProcessingOptions,
    config: ChunkingConfig
  ): boolean {
    // Si el chunking está deshabilitado
    if (!options.enableChunking) {
      return false;
    }

    // Si se fuerza el chunking, siempre hacer chunking independientemente del tamaño
    if (options.forceChunking) {
      return true;
    }

    // Si el documento es pequeño, no necesita chunking
    // Usar solo MIN_SIZE_FOR_CHUNKING como umbral, no maxChunkSize
    const threshold = CHUNKING_LIMITS.MIN_SIZE_FOR_CHUNKING;
    const contenidoLength = documento.contenido?.length || 0;
    if (contenidoLength <= threshold) {
      return false;
    }

    return true;
  }

  /**
   * Crea un resultado simple para documentos que no necesitan chunking
   */
  private static createSimpleResult(
    documento: Documento,
    config: ChunkingConfig,
    startTime: number
  ): DocumentChunkingResult {
    const processingTime = Date.now() - startTime;
    const contenido = documento.contenido || '';
    const contenidoLength = contenido.length;

    return {
      wasChunked: false,
      content: contenido,
      stats: {
        totalChunks: 1,
        averageChunkSize: contenidoLength,
        minChunkSize: contenidoLength,
        maxChunkSize: contenidoLength,
        totalContentSize: contenidoLength,
        sectionsDetected: 0,
        chunkingStrategy: 'none',
        processingTimeMs: processingTime
      },
      configUsed: config,
      sourceDocument: {
        id: documento.id,
        title: documento.titulo,
        originalSize: contenidoLength
      }
    };
  }

  /**
   * Realiza el chunking del documento
   */
  private static performChunking(
    documento: Documento,
    config: ChunkingConfig,
    options: DocumentProcessingOptions,
    startTime: number
  ): DocumentChunkingResult {
    // Generar ID único para la sesión de monitoreo
    const sessionId = `${documento.id}-${Date.now()}`;

    const contenido = documento.contenido || '';
    const contenidoLength = contenido.length;

    // Iniciar monitoreo de rendimiento
    chunkingPerformanceMonitor.startSession(
      sessionId,
      contenidoLength,
      options.contentType || 'default',
      {
        maxChunkSize: config.maxChunkSize,
        minChunkSize: config.minChunkSize || 0,
        overlapSize: config.overlapSize
      }
    );

    this.logger('info', 'Iniciando chunking de documento', {
      documentId: documento.id,
      titulo: documento.titulo,
      size: contenidoLength,
      config,
      sessionId
    });

    // Reportar progreso
    this.reportProgress(options, {
      phase: 'detecting_sections',
      percentage: 10,
      message: 'Detectando secciones del documento',
      chunksProcessed: 0,
      estimatedTotalChunks: 0
    });

    // Realizar chunking
    const chunks = dividirEnChunks(contenido, config, documento.id);

    // Actualizar métricas de monitoreo
    const basicStats = obtenerEstadisticasChunking(chunks);
    chunkingPerformanceMonitor.updateSession(sessionId, {
      chunksGenerated: chunks.length,
      sectionsDetected: basicStats.sectionsDetected,
      averageChunkSize: basicStats.averageSize
    });

    if (chunks.length > CHUNKING_LIMITS.MAX_CHUNKS_PER_DOCUMENT) {
      // Finalizar sesión de monitoreo antes de lanzar error
      chunkingPerformanceMonitor.endSession(sessionId);

      throw new ChunkingError(
        `Demasiados chunks generados: ${chunks.length} (máximo: ${CHUNKING_LIMITS.MAX_CHUNKS_PER_DOCUMENT})`,
        ChunkingErrorCode.CHUNK_TOO_LARGE,
        { documentId: documento.id, chunksGenerated: chunks.length }
      );
    }

    // Reportar progreso final
    this.reportProgress(options, {
      phase: 'finalizing',
      percentage: 100,
      message: 'Chunking completado',
      chunksProcessed: chunks.length,
      estimatedTotalChunks: chunks.length
    });

    const processingTime = Date.now() - startTime;
    const stats = this.createChunkingStats(chunks, processingTime);

    // Finalizar sesión de monitoreo
    const performanceMetrics = chunkingPerformanceMonitor.endSession(sessionId);

    this.logger('info', 'Chunking completado exitosamente', {
      documentId: documento.id,
      stats,
      performanceMetrics
    });

    return {
      wasChunked: true,
      content: chunks,
      stats,
      configUsed: config,
      sourceDocument: {
        id: documento.id,
        title: documento.titulo,
        originalSize: contenidoLength
      }
    };
  }

  /**
   * Crea estadísticas de chunking
   */
  private static createChunkingStats(chunks: Chunk[], processingTime: number): ChunkingStats {
    const basicStats = obtenerEstadisticasChunking(chunks);
    const firstChunk = chunks[0];
    
    return {
      totalChunks: basicStats.totalChunks,
      averageChunkSize: basicStats.averageSize,
      minChunkSize: basicStats.minSize,
      maxChunkSize: basicStats.maxSize,
      totalContentSize: basicStats.totalSize,
      sectionsDetected: basicStats.sectionsDetected,
      chunkingStrategy: firstChunk?.metadata.chunkType || 'section',
      processingTimeMs: processingTime
    };
  }

  /**
   * Reporta progreso del chunking
   */
  private static reportProgress(
    options: DocumentProcessingOptions,
    progress: ChunkingProgress
  ): void {
    if (options.onProgress) {
      options.onProgress(progress);
    }
  }

  /**
   * Crea un resultado de error para documentos que fallaron
   */
  private static createErrorResult(documento: Documento, _error: any): DocumentChunkingResult {
    // Usar contenido disponible o fallback a string vacío
    const contenidoFallback = documento.contenido || documento.contenido_corto || '';
    const contenidoTruncado = contenidoFallback.substring(0, 15000);
    const originalSize = contenidoFallback.length;

    return {
      wasChunked: false,
      content: contenidoTruncado,
      stats: {
        totalChunks: 1,
        averageChunkSize: contenidoTruncado.length,
        minChunkSize: contenidoTruncado.length,
        maxChunkSize: contenidoTruncado.length,
        totalContentSize: originalSize,
        sectionsDetected: 0,
        chunkingStrategy: 'none',
        processingTimeMs: 0
      },
      configUsed: getChunkingConfigForContentType('default'),
      sourceDocument: {
        id: documento.id,
        title: documento.titulo,
        originalSize: originalSize
      }
    };
  }

  /**
   * Obtiene métricas de rendimiento del servicio
   */
  static getPerformanceMetrics(): any {
    // TODO: Implementar sistema de métricas
    return {
      message: 'Métricas de rendimiento no implementadas aún'
    };
  }
}
