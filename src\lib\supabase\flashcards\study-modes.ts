/**
 * Modos de estudio para flashcards
 * 
 * Responsabilidades:
 * - Obtención de flashcards para diferentes modos de estudio
 * - Filtrado por dificultad, estado y fecha
 * - Algoritmos de selección de flashcards
 * - Funciones globales para estudio multi-colección
 */

import {
  supabase,
  FlashcardConProgreso
} from '../supabaseClient';
import { obtenerFlashcardsPorColeccionId } from './cards';
import { obtenerColeccionesFlashcards } from './collections';

/**
 * Obtiene todas las flashcards con su progreso para una colección
 */
export async function obtenerFlashcardsParaEstudiar(coleccionId: string): Promise<FlashcardConProgreso[]> {
  // Obtener todas las flashcards de la colección
  const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);

  // Obtener el progreso de todas las flashcards en una sola consulta
  const { data: progresos, error } = await supabase
    .from('progreso_flashcards')
    .select('*')
    .in('flashcard_id', flashcards.map(f => f.id));

  if (error) {
    console.error('Error al obtener progreso de flashcards:', error);
    return [];
  }

  // Fecha actual para comparar
  const ahora = new Date();
  const hoy = new Date(
    ahora.getFullYear(),
    ahora.getMonth(),
    ahora.getDate()
  );

  // Combinar flashcards con su progreso
  return flashcards.map(flashcard => {
    const progreso = progresos?.find(p => p.flashcard_id === flashcard.id);

    if (!progreso) {
      // Si no hay progreso, es una tarjeta nueva que debe estudiarse
      return {
        ...flashcard,
        debeEstudiar: true,
      };
    }

    // Determinar si la flashcard debe estudiarse hoy
    const proximaRevision = new Date(progreso.proxima_revision);
    const proximaRevisionSinHora = new Date(
      proximaRevision.getFullYear(),
      proximaRevision.getMonth(),
      proximaRevision.getDate()
    );
    const debeEstudiar = proximaRevisionSinHora <= hoy;

    return {
      ...flashcard,
      debeEstudiar,
      progreso: {
        factor_facilidad: progreso.factor_facilidad,
        intervalo: progreso.intervalo,
        repeticiones: progreso.repeticiones,
        estado: progreso.estado,
        proxima_revision: progreso.proxima_revision,
      },
    };
  });
}

/**
 * Obtiene flashcards más difíciles de una colección (basado en historial de revisiones)
 */
export async function obtenerFlashcardsMasDificiles(coleccionId: string, limite: number = 10): Promise<FlashcardConProgreso[]> {
  try {
    // Obtener todas las flashcards con progreso
    const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);

    // Obtener historial de revisiones para calcular dificultad
    const flashcardIds = flashcardsConProgreso.map(f => f.id);
    const { data: historial, error } = await supabase
      .from('historial_revisiones')
      .select('flashcard_id, dificultad')
      .in('flashcard_id', flashcardIds);

    if (error) {
      console.error('Error al obtener historial de revisiones:', error);
      return flashcardsConProgreso.slice(0, limite);
    }

    // Calcular estadísticas de dificultad por flashcard
    const estadisticasDificultad = new Map<string, { dificil: number; total: number }>();

    historial?.forEach(revision => {
      const stats = estadisticasDificultad.get(revision.flashcard_id) || { dificil: 0, total: 0 };
      stats.total++;
      if (revision.dificultad === 'dificil') {
        stats.dificil++;
      }
      estadisticasDificultad.set(revision.flashcard_id, stats);
    });

    // Ordenar por dificultad (ratio de respuestas difíciles)
    const flashcardsOrdenadas = flashcardsConProgreso
      .map(flashcard => {
        const stats = estadisticasDificultad.get(flashcard.id);
        const ratioDificultad = stats ? stats.dificil / stats.total : 0;
        return { ...flashcard, ratioDificultad };
      })
      .sort((a, b) => b.ratioDificultad - a.ratioDificultad)
      .slice(0, limite);

    return flashcardsOrdenadas;
  } catch (error) {
    console.error('Error al obtener flashcards más difíciles:', error);
    return [];
  }
}

/**
 * Obtiene flashcards aleatorias de una colección
 */
export async function obtenerFlashcardsAleatorias(coleccionId: string, limite: number = 10): Promise<FlashcardConProgreso[]> {
  try {
    const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);

    // Mezclar aleatoriamente y tomar el límite
    const flashcardsMezcladas = [...flashcardsConProgreso]
      .sort(() => Math.random() - 0.5)
      .slice(0, limite);

    return flashcardsMezcladas;
  } catch (error) {
    console.error('Error al obtener flashcards aleatorias:', error);
    return [];
  }
}

/**
 * Obtiene flashcards que no se han estudiado recientemente
 */
export async function obtenerFlashcardsNoRecientes(coleccionId: string, limite: number = 10): Promise<FlashcardConProgreso[]> {
  try {
    const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);

    // Obtener última revisión de cada flashcard
    const flashcardIds = flashcardsConProgreso.map(f => f.id);
    const { data: ultimasRevisiones, error } = await supabase
      .from('historial_revisiones')
      .select('flashcard_id, fecha')
      .in('flashcard_id', flashcardIds)
      .order('fecha', { ascending: false });

    if (error) {
      console.error('Error al obtener últimas revisiones:', error);
      return flashcardsConProgreso.slice(0, limite);
    }

    // Obtener la fecha más reciente por flashcard
    const ultimaRevisionPorFlashcard = new Map<string, string>();
    ultimasRevisiones?.forEach(revision => {
      if (!ultimaRevisionPorFlashcard.has(revision.flashcard_id)) {
        ultimaRevisionPorFlashcard.set(revision.flashcard_id, revision.fecha);
      }
    });

    // Ordenar por fecha de última revisión (más antiguas primero)
    const flashcardsOrdenadas = flashcardsConProgreso
      .sort((a, b) => {
        const ultimaRevisionA = ultimaRevisionPorFlashcard.get(a.id);
        const ultimaRevisionB = ultimaRevisionPorFlashcard.get(b.id);
        const fechaA = ultimaRevisionA ? new Date(ultimaRevisionA).getTime() : 0;
        const fechaB = ultimaRevisionB ? new Date(ultimaRevisionB).getTime() : 0;
        return fechaA - fechaB;
      })
      .slice(0, limite);

    return flashcardsOrdenadas;
  } catch (error) {
    console.error('Error al obtener flashcards no recientes:', error);
    return [];
  }
}

/**
 * Obtiene flashcards por estado específico
 */
export async function obtenerFlashcardsPorEstado(
  coleccionId: string,
  estado: 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendido',
  limite: number = 10
): Promise<FlashcardConProgreso[]> {
  try {
    const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);

    // Filtrar por estado y limitar
    const flashcardsFiltradas = flashcardsConProgreso
      .filter(flashcard => {
        if (!flashcard.progreso) {
          return estado === 'nuevo';
        }
        return flashcard.progreso.estado === estado;
      })
      .slice(0, limite);

    return flashcardsFiltradas;
  } catch (error) {
    console.error('Error al obtener flashcards por estado:', error);
    return [];
  }
}
