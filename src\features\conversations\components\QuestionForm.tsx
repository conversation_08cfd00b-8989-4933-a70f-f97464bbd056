import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useMobileDetection } from '../../../hooks/useMobileDetection';
import {
  Documento,
  crearConversacion,
  guardarMensaje,
  obtenerMensajesPorConversacionId,
  actualizarConversacion,
  activarConversacion,
  obtenerConversacionActiva,
  desactivarTodasLasConversaciones
} from '../../../lib/supabase/index';
import ConversationSidebar from './ConversationSidebar';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { preguntaFormSchema } from '../../../lib/formSchemas';
import { z } from 'zod';
import { useAuth } from '@/contexts/AuthContext';
import { usePlanLimits } from '@/hooks/usePlanLimits';
import AiTaskHeader from '@/components/ui/AiTaskHeader';
import { FiMessageSquare } from 'react-icons/fi';
import { checkUserFeatureAccess } from '@/config/plans';
import UpgradePlanMessage from '@/components/ui/UpgradePlanMessage';

interface QuestionFormProps {
  documentoSeleccionado: Documento | null;
}

interface Mensaje {
  tipo: 'usuario' | 'ia';
  contenido: string;
  timestamp: Date;
  id?: string;
}

export default function QuestionForm({ documentoSeleccionado }: QuestionFormProps) {
  const [mensajes, setMensajes] = useState<Mensaje[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [conversacionActualId, setConversacionActualId] = useState<string | null>(null);
  const [guardandoConversacion, setGuardandoConversacion] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { user } = useAuth();
  const [userPlan, setUserPlan] = useState<string | null>(null);
  const { isMobile } = useMobileDetection();
  const { refresh: refreshPlanLimits } = usePlanLimits();



  const {
    register,
    handleSubmit: handleSubmitForm,
    formState: { errors },
    reset,
    setValue,
  } = useForm<z.infer<typeof preguntaFormSchema>>({
    resolver: zodResolver(preguntaFormSchema),
    defaultValues: {
      pregunta: '',
      documento: documentoSeleccionado,
    },
  });

  // Sincronizar documento seleccionado con el formulario, asegurando tipos correctos
  useEffect(() => {
    if (documentoSeleccionado && documentoSeleccionado.id) {
      const documentoValidado = {
        ...documentoSeleccionado,
        id: documentoSeleccionado.id, // Asegurar que id existe
        categoria: documentoSeleccionado.categoria || null,
        numero_tema: documentoSeleccionado.numero_tema !== undefined && documentoSeleccionado.numero_tema !== null
          ? (typeof documentoSeleccionado.numero_tema === 'string' ? parseInt(documentoSeleccionado.numero_tema, 10) : documentoSeleccionado.numero_tema)
          : undefined,
        // CORRECCIÓN: No modificar el campo contenido para documentos de Storage
        // El endpoint de IA se encargará de obtener el contenido desde Storage si es necesario
        // Solo asegurar que contenido esté presente si ya existe
        ...(documentoSeleccionado.contenido && { contenido: documentoSeleccionado.contenido }),
        // Asegurar que todos los campos opcionales estén presentes
        creado_en: documentoSeleccionado.creado_en || undefined,
        actualizado_en: documentoSeleccionado.actualizado_en || undefined,
        user_id: documentoSeleccionado.user_id || undefined,
        tipo_original: documentoSeleccionado.tipo_original || undefined,
      };

      setValue('documento', documentoValidado);
    } else {
      setValue('documento', null);
    }
  }, [documentoSeleccionado, setValue]);

  // Efecto para verificar el plan del usuario
  useEffect(() => {
    const checkUserPlan = async () => {
      if (user) {
        const hasAccess = await checkUserFeatureAccess('ai_tutor_chat');
        setUserPlan(hasAccess ? 'paid' : 'free');
      }
    };
    checkUserPlan();
  }, [user]);

  // Función para cargar una conversación desde Supabase
  const cargarConversacion = useCallback(async (conversacionId: string) => {
    try {
      setIsLoading(true);

      // Activar la conversación seleccionada
      await activarConversacion(conversacionId);

      // Obtener los mensajes de la conversación
      const mensajesDB = await obtenerMensajesPorConversacionId(conversacionId);

      // Convertir los mensajes de la base de datos al formato local
      const mensajesFormateados: Mensaje[] = mensajesDB.map(msg => ({
        id: msg.id,
        tipo: msg.tipo,
        contenido: msg.contenido,
        timestamp: new Date(msg.timestamp)
      }));

      // Actualizar el estado
      setMensajes(mensajesFormateados);
      setConversacionActualId(conversacionId);
      setError('');

      // Cerrar el sidebar en móvil después de seleccionar conversación
      if (isMobile) {
        setShowSidebar(false);
      }


    } catch (error) {
      console.error('Error al cargar la conversación:', error);
      setError('No se pudo cargar la conversación');
    } finally {
      setIsLoading(false);
    }
  }, [isMobile]);

  // Efecto para cargar la conversación activa al iniciar
  useEffect(() => {
    const cargarConversacionActiva = async () => {
      try {
        const conversacionActiva = await obtenerConversacionActiva();

        if (conversacionActiva) {

          setConversacionActualId(conversacionActiva.id);
          await cargarConversacion(conversacionActiva.id);
        } else {

          setMensajes([]);
          setConversacionActualId(null);
        }
      } catch (error) {
        console.warn('No se pudo cargar la conversación activa (esto es normal para usuarios nuevos):', error);
        // No mostrar error al usuario, simplemente inicializar sin conversación
        setMensajes([]);
        setConversacionActualId(null);
      }
    };

    // Solo cargar conversaciones si el usuario tiene acceso
    if (userPlan === 'paid') {
      cargarConversacionActiva();
    }
  }, [userPlan, cargarConversacion]);

  // Efecto para hacer scroll al último mensaje cuando se añade uno nuevo
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [mensajes]);



  // Función para guardar un mensaje en Supabase
  const guardarMensajeEnDB = async (mensaje: Mensaje, conversacionIdForzado?: string): Promise<string | null> => {
    try {
      setGuardandoConversacion(true);
      const conversacionId = conversacionIdForzado || conversacionActualId;

      // Si no hay una conversación actual, crear una nueva
      if (!conversacionId) {
        // Solo crear una nueva conversación si es el primer mensaje del usuario
        if (mensaje.tipo === 'usuario') {
          // Crear un título basado en la primera pregunta
          const titulo = `Conversación: ${mensaje.contenido.substring(0, 50)}${mensaje.contenido.length > 50 ? '...' : ''}`;

          // Crear una nueva conversación y marcarla como activa
          const nuevoId = await crearConversacion(titulo, true);
          if (!nuevoId) {
            throw new Error('No se pudo crear la conversación');
          }


          // Guardar el ID de la conversación para futuros mensajes
          setConversacionActualId(nuevoId);

          // Guardar el mensaje en la nueva conversación
          await guardarMensaje({
            conversacion_id: nuevoId,
            tipo: mensaje.tipo,
            contenido: mensaje.contenido
          });

          // Retornar el ID de la nueva conversación
          return nuevoId;
        } else {
          // Si es un mensaje de la IA pero no hay conversación actual,
          // esto es un error crítico que no debería pasar
          console.error('❌ ERROR CRÍTICO: Intentando guardar mensaje de IA sin conversación activa');
          throw new Error('No se puede guardar un mensaje de IA sin una conversación activa');
        }
      } else {
        // Verificar que la conversación actual sigue siendo la activa
        const conversacionActiva = await obtenerConversacionActiva();

        if (!conversacionActiva || conversacionActiva.id !== conversacionId) {
          // Si la conversación actual no es la activa, activarla
          await activarConversacion(conversacionId);
        }

        // Guardar el mensaje en la conversación existente
        await guardarMensaje({
          conversacion_id: conversacionId,
          tipo: mensaje.tipo,
          contenido: mensaje.contenido
        });

        // Retornar el ID de la conversación existente
        return conversacionId;
      }
    } catch (error) {
      console.error('Error al guardar el mensaje:', error);
      // No mostramos error al usuario para no interrumpir la experiencia
      return null;
    } finally {
      setGuardandoConversacion(false);
    }
  };

  // Función para iniciar una nueva conversación
  const iniciarNuevaConversacion = async () => {
    try {
      setIsLoading(true);

      // Desactivar todas las conversaciones en la base de datos
      await desactivarTodasLasConversaciones();

      // Limpiar los mensajes actuales
      setMensajes([]);
      // Establecer el ID de conversación a null para que se cree una nueva en el próximo mensaje
      setConversacionActualId(null);
      setError('');

      
    } catch (error) {
      console.error('Error al iniciar nueva conversación:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Función para manejar cuando se elimina una conversación
  const handleConversationDeleted = () => {
    // Si se eliminó la conversación actual, limpiar el estado
    setMensajes([]);
    setConversacionActualId(null);
    setError('');
  };

  // Función de envío personalizada que bypassa validación si es necesario
  const handleCustomSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Obtener el valor actual del campo pregunta
    const preguntaValue = (e.target as HTMLFormElement).pregunta.value;

    if (!preguntaValue || preguntaValue.trim() === '') {
      setError('La pregunta es obligatoria');
      return;
    }

    if (!documentoSeleccionado) {
      setError('Debes seleccionar un documento');
      return;
    }

    // Crear datos manualmente
    const data = {
      pregunta: preguntaValue.trim(),
      documento: documentoSeleccionado
    };

    await onSubmit(data);
  };

  // Cambia handleSubmit para usar React Hook Form
  const onSubmit = async (data: z.infer<typeof preguntaFormSchema>) => {
    setIsLoading(true);
    setError('');

    // Añadir la pregunta del usuario al historial
    const preguntaUsuario: Mensaje = {
      tipo: 'usuario',
      contenido: data.pregunta,
      timestamp: new Date()
    };

    setMensajes(prevMensajes => [...prevMensajes, preguntaUsuario]);
    setIsLoading(true);
    setError('');

    // Limpiar el campo de pregunta después de enviarla
    const form = document.getElementById('pregunta') as HTMLTextAreaElement;
    if (form) form.value = '';

    let conversacionId: string | null = null;

    try {
      // Guardar la pregunta del usuario en Supabase
      conversacionId = await guardarMensajeEnDB(preguntaUsuario);

      // Pasar los documentos completos a la función obtenerRespuestaIA
      // No solo el contenido, sino también el título, categoría y número de tema

      // Obtener respuesta de la IA
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pregunta: preguntaUsuario.contenido,
          documentos: data.documento ? [data.documento] : []
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Error ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const respuestaIA = await response.json();
      let respuestaTexto = '';
      if (respuestaIA.result) {
        respuestaTexto = typeof respuestaIA.result === 'string' ? respuestaIA.result : JSON.stringify(respuestaIA.result);
      } else if (respuestaIA.error) {
        respuestaTexto = typeof respuestaIA.error === 'string' ? respuestaIA.error : JSON.stringify(respuestaIA.error);
      } else {
        respuestaTexto = 'Error desconocido al obtener respuesta de la IA.';
      }

      // Verificar que tenemos una conversación activa antes de guardar la respuesta de la IA
      if (!conversacionId) {
        console.error('❌ ERROR: No se pudo obtener el ID de conversación para guardar la respuesta de la IA');
        throw new Error('No se pudo guardar la respuesta: conversación no encontrada');
      }

      // Asegurar que el estado local esté sincronizado
      if (conversacionActualId !== conversacionId) {
        setConversacionActualId(conversacionId);
      }

      // Añadir la respuesta de la IA al historial
      const mensajeIA: Mensaje = {
        tipo: 'ia',
        contenido: respuestaTexto,
        timestamp: new Date()
      };

      setMensajes(prevMensajes => [...prevMensajes, mensajeIA]);

      // Guardar la respuesta de la IA en Supabase usando el ID de conversación correcto
      await guardarMensajeEnDB(mensajeIA, conversacionId);

      // Refrescar límites después de respuesta exitosa de IA
      refreshPlanLimits();

      // Si es la primera pregunta, actualizar el título de la conversación con un título más descriptivo
      if (mensajes.length === 0 && conversacionId) {
        const tituloMejorado = `Conversación: ${data.pregunta.substring(0, 50)}${data.pregunta.length > 50 ? '...' : ''}`;
        await actualizarConversacion(conversacionId, tituloMejorado);
      }
    } catch (error) {
      console.error('Error al obtener respuesta:', error);

      // Determinar el tipo de error y mostrar un mensaje más específico
      let mensajeError = 'Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo.';

      if (error instanceof Error) {
        if (error.message.includes('API key')) {
          mensajeError = 'Error de configuración: La clave de API de Gemini no está configurada correctamente.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          mensajeError = 'Error de conexión: No se pudo conectar con el servicio de IA. Verifica tu conexión a internet.';
        } else if (error.message.includes('quota') || error.message.includes('limit')) {
          mensajeError = 'Se ha alcanzado el límite de uso del servicio de IA. Inténtalo más tarde.';
        } else {
          mensajeError = `Error: ${error.message}`;
        }
      }

      setError(mensajeError);

      // Añadir mensaje de error como respuesta de la IA
      const mensajeErrorIA: Mensaje = {
        tipo: 'ia',
        contenido: mensajeError,
        timestamp: new Date()
      };

      setMensajes(prevMensajes => [...prevMensajes, mensajeErrorIA]);

      // Intentar guardar el mensaje de error en Supabase (sin fallar si no se puede)
      try {
        // Usar conversacionId si está disponible, sino usar conversacionActualId
        const idParaError = conversacionId || conversacionActualId;
        if (idParaError) {
          await guardarMensajeEnDB(mensajeErrorIA, idParaError);
        }
      } catch (dbError) {
        console.error('Error al guardar mensaje de error en DB:', dbError);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Formatear la fecha para mostrarla en el chat
  const formatearFecha = (fecha: Date): string => {
    return fecha.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Si el usuario tiene plan gratuito, mostrar mensaje de bloqueo
  if (userPlan === 'free') {
    return (
      <div className="mt-6">
        <UpgradePlanMessage
          feature="ai_tutor_chat"
          benefits={[
            "Chat ilimitado con IA especializada",
            "Respuestas personalizadas a tus documentos",
            "Historial completo de conversaciones",
            "Explicaciones detalladas y ejemplos"
          ]}
          className="h-[600px]"
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AiTaskHeader
        icon={<FiMessageSquare className="w-5 h-5" />}
        title="Habla con tu Preparador"
        documentoSeleccionado={documentoSeleccionado}
      />

      <div className="flex h-[600px] gap-6 relative">
      {/* Panel principal del chat - ocupa todo el espacio disponible */}
      <div className={`flex flex-col ${showSidebar && !isMobile ? 'flex-1' : 'w-full'}`}>
        {/* Botones para nueva conversación e historial */}
        <div className="flex justify-between mb-4">
          <button
            type="button"
            onClick={iniciarNuevaConversacion}
            className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center"
            disabled={isLoading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            Nueva conversación
          </button>

          <button
            type="button"
            onClick={() => setShowSidebar(!showSidebar)}
            className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
            {showSidebar ? 'Ocultar historial' : 'Historial'}
          </button>
        </div>

        {/* Contenedor del chat con historial de mensajes */}
        <div
          ref={chatContainerRef}
          className="flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50"
          style={{ height: 'calc(100% - 180px)' }}
        >
        {mensajes.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <p>Selecciona un documento y haz una pregunta para comenzar la conversación.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {mensajes.map((mensaje, index) => (
              <div
                key={mensaje.id || index}
                className={`flex ${mensaje.tipo === 'usuario' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`${isMobile ? 'max-w-[95%]' : 'max-w-[80%]'} p-3 rounded-lg ${
                    mensaje.tipo === 'usuario'
                      ? 'bg-blue-500 text-white rounded-br-none'
                      : 'bg-white border border-gray-300 rounded-bl-none'
                  }`}
                >
                  <div className="whitespace-pre-wrap">{mensaje.contenido}</div>
                  <div
                    className={`text-xs mt-1 text-right ${
                      mensaje.tipo === 'usuario' ? 'text-blue-100' : 'text-gray-500'
                    }`}
                  >
                    {formatearFecha(mensaje.timestamp)}
                  </div>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className={`${isMobile ? 'max-w-[95%]' : 'max-w-[80%]'} bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none`}>
                  <div className="flex items-center space-x-2">
                    <div className="animate-bounce h-2 w-2 bg-gray-500 rounded-full"></div>
                    <div className="animate-bounce h-2 w-2 bg-gray-500 rounded-full" style={{ animationDelay: '0.2s' }}></div>
                    <div className="animate-bounce h-2 w-2 bg-gray-500 rounded-full" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              </div>
            )}

            {guardandoConversacion && (
              <div className="text-xs text-gray-500 text-center py-1">
                Guardando conversación...
              </div>
            )}
          </div>
        )}
        </div>

        {/* Formulario para enviar preguntas */}
        <form onSubmit={handleCustomSubmit} className="mt-auto">
          {error && (
            <div className="text-red-500 text-sm mb-2">{error}</div>
          )}

          {/* Indicador de documento seleccionado */}
          <div className="text-xs text-gray-600 mb-2">
            {documentoSeleccionado ? (
              <span className="text-green-600">
                ✓ 1 documento seleccionado: {documentoSeleccionado.titulo}
              </span>
            ) : (
              <span className="text-red-600">
                ⚠ No hay documento seleccionado. Selecciona uno para hacer preguntas.
              </span>
            )}
          </div>

          <div className="flex items-end space-x-2">
            <div className="flex-grow">
              <textarea
                id="pregunta"
                name="pregunta"
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                rows={2}
                placeholder="Escribe tu pregunta sobre el documento seleccionado..."
                disabled={isLoading}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleCustomSubmit(e as any);
                  }
                }}
              />
              {errors.pregunta && (
                <p className="text-red-500 text-xs mt-1">{errors.pregunta.message}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">Presiona Enter para enviar, Shift+Enter para nueva línea</p>
            </div>

            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading || !documentoSeleccionado}
              title={!documentoSeleccionado ? 'Selecciona un documento para hacer una pregunta' : 'Enviar pregunta'}
            >
              {isLoading ? (
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg className="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path>
                </svg>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Panel lateral de conversaciones - responsive */}
      {showSidebar && (
        <>
          {/* Overlay de fondo en móvil */}
          {isMobile && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setShowSidebar(false)}
            />
          )}

          {/* Sidebar - overlay en móvil, normal en desktop */}
          <div className={isMobile ? 'fixed top-0 right-0 z-50 w-80 h-screen' : ''}>
            <ConversationSidebar
              onSelectConversation={cargarConversacion}
              conversacionActualId={conversacionActualId}
              onConversationDeleted={handleConversationDeleted}
              onClose={() => setShowSidebar(false)}
            />
          </div>
        </>
      )}
      </div>
    </div>
  );
}
