'use client';

import React from 'react';

interface RecommendationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  closeButtonText: string;
}

export function RecommendationsModal({
  isOpen,
  onClose,
  title,
  children,
  closeButtonText
}: RecommendationsModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {title}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors text-xl font-bold"
          >
            ×
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          <div className="prose prose-sm max-w-none text-gray-700">
            {children}
          </div>
        </div>
        
        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            {closeButtonText}
          </button>
        </div>
      </div>
    </div>
  );
}

// Componente específico para Tests
export function TestRecommendationsModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  return (
    <RecommendationsModal
      isOpen={isOpen}
      onClose={onClose}
      title="🎯 Cómo Crear Tests de Alta Calidad"
      closeButtonText="Entendido, ¡a por el test!"
    >
      <div className="space-y-4">
        <p className="text-base leading-relaxed">
          Para que la IA genere las preguntas más útiles para tu estudio, sigue estas recomendaciones:
        </p>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              📍 Enfócate en lo Concreto:
            </h3>
            <p className="mb-2">Pide preguntas sobre un apartado, ley o concepto específico.</p>
            <div className="bg-green-50 border-l-4 border-green-400 p-3 mb-2">
              <p className="text-sm">
                <strong>Ejemplo Bueno:</strong> <em>"Crea un test sobre las competencias exclusivas del Estado del Título VIII de la Constitución."</em>
              </p>
            </div>
            <div className="bg-red-50 border-l-4 border-red-400 p-3">
              <p className="text-sm">
                <strong>Ejemplo a Evitar:</strong> <em>"Hazme preguntas de la Constitución."</em>
              </p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              🎯 Especifica el Tipo de Pregunta:
            </h3>
            <p className="mb-2">¿Necesitas repasar fechas, plazos o definiciones? ¡Díselo!</p>
            <div className="bg-blue-50 border-l-4 border-blue-400 p-3">
              <p className="text-sm">
                <strong>Ejemplo:</strong> <em>"Genera 15 preguntas enfocadas en los plazos del procedimiento administrativo común."</em>
              </p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              🔢 Usa el Número del Apartado:
            </h3>
            <p className="mb-2">Si tu temario está bien estructurado, usar el número del apartado es la forma más precisa de guiar a la IA.</p>
            <div className="bg-purple-50 border-l-4 border-purple-400 p-3">
              <p className="text-sm">
                <strong>Ejemplo:</strong> <em>"Un test sobre el subapartado 2.3: Los interesados en el procedimiento."</em>
              </p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
          <p className="text-sm font-medium text-yellow-800">
            💡 <strong>Recuerda:</strong> Cuanto más precisa sea tu petición, más se parecerá el test a uno de examen real.
          </p>
        </div>
      </div>
    </RecommendationsModal>
  );
}

// Componente específico para Flashcards
export function FlashcardRecommendationsModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  return (
    <RecommendationsModal
      isOpen={isOpen}
      onClose={onClose}
      title="📚 Cómo Crear Flashcards Efectivas"
      closeButtonText="Entendido, ¡a memorizar!"
    >
      <div className="space-y-4">
        <p className="text-base leading-relaxed">
          Las mejores flashcards son concisas y se centran en un único concepto. Ayuda a la IA a crearlas así:
        </p>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              📖 Pide Definiciones y Listas:
            </h3>
            <p className="mb-2">Las flashcards son ideales para memorizar.</p>
            <div className="bg-green-50 border-l-4 border-green-400 p-3 mb-2">
              <p className="text-sm">
                <strong>Ejemplo:</strong> <em>"Genera flashcards con las definiciones de acto nulo y anulable."</em>
              </p>
            </div>
            <div className="bg-green-50 border-l-4 border-green-400 p-3">
              <p className="text-sm">
                <strong>Ejemplo:</strong> <em>"Crea flashcards para los principios del artículo 103 de la Constitución."</em>
              </p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              🎯 Enfócate en un Tema Pequeño:
            </h3>
            <p className="mb-2">Es mejor generar 10 flashcards muy específicas de un apartado, que 10 flashcards genéricas de todo un tema.</p>
            <div className="bg-blue-50 border-l-4 border-blue-400 p-3">
              <p className="text-sm">
                <strong>Ejemplo:</strong> <em>"10 flashcards sobre las fases del procedimiento administrativo."</em>
              </p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              📊 Ideal para Datos Puros:
            </h3>
            <p className="mb-2">Úsalas para memorizar fechas, plazos, artículos clave y cifras importantes.</p>
            <div className="bg-purple-50 border-l-4 border-purple-400 p-3">
              <p className="text-sm">
                <strong>Ejemplo:</strong> <em>"Flashcards con los plazos para interponer el recurso de alzada y de reposición."</em>
              </p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
          <p className="text-sm font-medium text-yellow-800">
            💡 <strong>Recuerda:</strong> Una buena flashcard debe tener una pregunta corta y una respuesta directa. ¡Guía a la IA para que las cree así!
          </p>
        </div>
      </div>
    </RecommendationsModal>
  );
}

// Exportación por defecto para compatibilidad con tests
export default RecommendationsModal;

// Componente específico para Mapas Mentales
export function MindMapRecommendationsModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  return (
    <RecommendationsModal
      isOpen={isOpen}
      onClose={onClose}
      title="🗺️ Cómo Generar Mapas Mentales Claros"
      closeButtonText="Entendido, ¡a visualizar!"
    >
      <div className="space-y-4">
        <p className="text-base leading-relaxed">
          Un mapa mental debe mostrar la estructura y las relaciones entre ideas. Para obtener el mejor resultado visual:
        </p>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              🎯 Define el Nodo Central:
            </h3>
            <p className="mb-2">Tu petición debe indicar cuál es el concepto principal que irá en el centro del mapa.</p>
            <div className="bg-green-50 border-l-4 border-green-400 p-3">
              <p className="text-sm">
                <strong>Ejemplo:</strong> <em>"Crea un mapa mental sobre la estructura y tipos de Reglamentos."</em>
              </p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              📊 Pide Jerarquía:
            </h3>
            <p className="mb-2">Pide a la IA que organice la información de forma jerárquica.</p>
            <div className="bg-blue-50 border-l-4 border-blue-400 p-3">
              <p className="text-sm">
                <strong>Ejemplo:</strong> <em>"Genera un mapa mental que muestre la jerarquía normativa en España."</em>
              </p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-2">
              🔍 Limita el Alcance:
            </h3>
            <p className="mb-2">Un mapa mental es más útil si se centra en un solo tema o en la relación entre 2-3 conceptos clave. Evita peticiones demasiado amplias.</p>
            <div className="bg-green-50 border-l-4 border-green-400 p-3 mb-2">
              <p className="text-sm">
                <strong>Ejemplo Bueno:</strong> <em>"Un mapa mental de las fases del procedimiento administrativo común."</em>
              </p>
            </div>
            <div className="bg-red-50 border-l-4 border-red-400 p-3">
              <p className="text-sm">
                <strong>Ejemplo a Evitar:</strong> <em>"Un mapa mental de todo el derecho administrativo."</em>
              </p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
          <p className="text-sm font-medium text-yellow-800">
            💡 <strong>Recuerda:</strong> Piensa en el mapa como el índice visual de un tema. ¡Sé claro en lo que quieres estructurar!
          </p>
        </div>
      </div>
    </RecommendationsModal>
  );
}
