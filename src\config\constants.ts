// src/config/constants.ts
// Constantes centralizadas del sistema

// ============================================================================
// CONSTANTES DE APLICACIÓN
// ============================================================================

/**
 * URLs y rutas de la aplicación
 */
export const APP_URLS = {
  BASE: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  SITE: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  UPGRADE_PLAN: '/upgrade-plan',
  THANK_YOU: '/thank-you',
  LOGIN: '/login',
  DASHBOARD: '/app',
  PROFILE: '/profile',
  WELCOME: '/welcome'
} as const;

/**
 * Rutas públicas (no requieren autenticación)
 */
export const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/payment',
  '/thank-you',
  '/contact',
  '/privacy',
  '/terms',
  '/politica-de-cookies',
  '/politica-de-privacidad',
  '/terminos-de-servicio',
  '/auth/callback',
  '/auth/confirmed',
  '/auth/unauthorized',
  '/auth/reset-password',
  '/auth/confirm-reset',
  '/api/auth/register-free',
  '/api/auth/pre-register-paid',
  '/api/stripe/webhook',
  '/api/stripe/create-checkout-session',
  '/api/stripe/create-token-checkout',
  '/api/notify-signup',
  '/api/user/status',
  '/api/health',
  '/api/auth/initiate-password-setup'
] as const;

/**
 * Rutas que requieren autenticación básica
 */
export const AUTHENTICATED_ROUTES = [
  '/app',
  '/dashboard',
  '/profile',
  '/welcome',
  '/upgrade-plan'
] as const;

// ============================================================================
// CONSTANTES DE VALIDACIÓN Y LÍMITES
// ============================================================================

/**
 * Límites de archivos y uploads
 */
export const FILE_LIMITS = {
  MAX_SIZE_MB: 5,
  MAX_SIZE_BYTES: 5 * 1024 * 1024,
  ALLOWED_TYPES: ['application/pdf'],
  ALLOWED_EXTENSIONS: ['.pdf']
} as const;

/**
 * Límites de texto y contenido
 */
export const TEXT_LIMITS = {
  MIN_PASSWORD_LENGTH: 8,
  MAX_PASSWORD_LENGTH: 128,
  MAX_DOCUMENT_TITLE_LENGTH: 200,
  MAX_DESCRIPTION_LENGTH: 500,
  MIN_CONTENT_LENGTH: 100,
  MAX_CONTENT_LENGTH: 50000
} as const;

/**
 * Límites de tokens y uso
 */
export const TOKEN_LIMITS = {
  DEFAULT_FREE_LIMIT: 50000,
  WARNING_THRESHOLD_PERCENTAGE: 80,
  CRITICAL_THRESHOLD_PERCENTAGE: 90,
  EXCEEDED_THRESHOLD_PERCENTAGE: 100
} as const;

/**
 * Límites de rate limiting
 */
export const RATE_LIMITS = {
  DEFAULT_WINDOW_MINUTES: 60,
  DEFAULT_MAX_REQUESTS: 100,
  API_REQUESTS_PER_MINUTE: 60,
  UPLOAD_REQUESTS_PER_HOUR: 10,
  AUTH_ATTEMPTS_PER_HOUR: 5
} as const;

// ============================================================================
// CONSTANTES DE TIEMPO Y CONFIGURACIÓN
// ============================================================================

/**
 * Timeouts y intervalos
 */
export const TIMEOUTS = {
  SESSION_TIMEOUT_MS: 5 * 60 * 1000, // 5 minutos
  API_TIMEOUT_MS: 30 * 1000, // 30 segundos
  UPLOAD_TIMEOUT_MS: 60 * 1000, // 1 minuto
  RETRY_DELAY_MS: 1000, // 1 segundo
  POLLING_INTERVAL_MS: 2000 // 2 segundos
} as const;

/**
 * Configuración de reintentos
 */
export const RETRY_CONFIG = {
  MAX_ATTEMPTS: 3,
  BACKOFF_MULTIPLIER: 2,
  INITIAL_DELAY_MS: 1000
} as const;

/**
 * Configuración de seguridad
 */
export const SECURITY_CONFIG = {
  ENABLE_STRICT_VALIDATION: process.env.STRICT_PLAN_VALIDATION === 'true',
  REQUIRE_PAYMENT_VERIFICATION: process.env.REQUIRE_PAYMENT_VERIFICATION === 'true',
  ENABLE_ACCESS_LOGGING: process.env.ENABLE_ACCESS_LOGGING === 'true',
  ENABLE_FEATURE_VALIDATION: process.env.ENABLE_FEATURE_VALIDATION === 'true',
  AUTO_ACTIVATE_PAYMENTS: process.env.AUTO_ACTIVATE_PAYMENTS === 'true',
  ENABLE_PUBLIC_SIGNUP: process.env.ENABLE_PUBLIC_SIGNUP === 'true'
} as const;

// ============================================================================
// CONSTANTES DE MENSAJES Y TEXTOS
// ============================================================================

/**
 * Mensajes de error comunes
 */
export const ERROR_MESSAGES = {
  UNAUTHORIZED: 'No autorizado',
  FORBIDDEN: 'Acceso denegado',
  NOT_FOUND: 'Recurso no encontrado',
  INTERNAL_ERROR: 'Error interno del servidor',
  INVALID_DATA: 'Datos inválidos',
  USER_NOT_FOUND: 'Usuario no encontrado',
  PROFILE_NOT_FOUND: 'Perfil de usuario no encontrado',
  PAYMENT_REQUIRED: 'Pago requerido',
  LIMIT_EXCEEDED: 'Límite excedido',
  FILE_TOO_LARGE: 'El archivo es demasiado grande',
  INVALID_FILE_TYPE: 'Tipo de archivo no válido',
  UPLOAD_FAILED: 'Error al subir el archivo',
  PROCESSING_ERROR: 'Error al procesar la solicitud',
  NETWORK_ERROR: 'Error de conexión',
  TIMEOUT_ERROR: 'Tiempo de espera agotado'
} as const;

/**
 * Mensajes de éxito
 */
export const SUCCESS_MESSAGES = {
  UPLOAD_SUCCESS: 'Archivo subido correctamente',
  SAVE_SUCCESS: 'Guardado correctamente',
  UPDATE_SUCCESS: 'Actualizado correctamente',
  DELETE_SUCCESS: 'Eliminado correctamente',
  PAYMENT_SUCCESS: 'Pago procesado correctamente',
  REGISTRATION_SUCCESS: 'Registro completado',
  LOGIN_SUCCESS: 'Sesión iniciada',
  LOGOUT_SUCCESS: 'Sesión cerrada',
  PASSWORD_RESET_SUCCESS: 'Contraseña restablecida',
  EMAIL_SENT: 'Email enviado correctamente'
} as const;

/**
 * Mensajes de validación
 */
export const VALIDATION_MESSAGES = {
  REQUIRED_FIELD: 'Este campo es obligatorio',
  INVALID_EMAIL: 'Email no válido',
  PASSWORD_TOO_SHORT: `La contraseña debe tener al menos ${TEXT_LIMITS.MIN_PASSWORD_LENGTH} caracteres`,
  PASSWORD_TOO_LONG: `La contraseña no puede tener más de ${TEXT_LIMITS.MAX_PASSWORD_LENGTH} caracteres`,
  PASSWORDS_DONT_MATCH: 'Las contraseñas no coinciden',
  INVALID_FILE_SIZE: `El archivo no puede superar ${FILE_LIMITS.MAX_SIZE_MB}MB`,
  INVALID_FILE_FORMAT: 'Formato de archivo no válido',
  TEXT_TOO_SHORT: `El texto debe tener al menos ${TEXT_LIMITS.MIN_CONTENT_LENGTH} caracteres`,
  TEXT_TOO_LONG: `El texto no puede superar ${TEXT_LIMITS.MAX_CONTENT_LENGTH} caracteres`
} as const;

// ============================================================================
// CONSTANTES DE ESTADO Y TIPOS
// ============================================================================

/**
 * Estados de procesamiento
 */
export const PROCESSING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  PROCESSING: 'processing',
  SUCCESS: 'success',
  ERROR: 'error',
  CANCELLED: 'cancelled'
} as const;

/**
 * Estados de pago
 */
export const PAYMENT_STATES = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded'
} as const;

/**
 * Tipos de notificación
 */
export const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error'
} as const;

/**
 * Niveles de severidad
 */
export const SEVERITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const;

// ============================================================================
// CONSTANTES DE CONFIGURACIÓN DE ENTORNO
// ============================================================================

/**
 * Variables de entorno requeridas
 */
export const REQUIRED_ENV_VARS = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'STRIPE_SECRET_KEY',
  'STRIPE_WEBHOOK_SECRET',
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'
] as const;

/**
 * Configuración de automatización
 */
export const AUTOMATION_CONFIG = {
  INVITATION_EXPIRY_HOURS: parseInt(process.env.INVITATION_EXPIRY_HOURS || '24'),
  DEFAULT_TRIAL_DAYS: 5,
  DEFAULT_GRACE_PERIOD_DAYS: 3
} as const;

// ============================================================================
// CONSTANTES DE BUSINESS LOGIC
// ============================================================================

/**
 * Costos y precios (en centavos)
 */
export const PRICING = {
  ADDITIONAL_TOKENS_PRICE: 1000, // €10.00 por 1M tokens
  ADDITIONAL_TOKENS_AMOUNT: 1000000,
  FREE_PLAN_PRICE: 0,
  BASIC_PLAN_PRICE: 999, // €9.99
  PRO_PLAN_PRICE: 1999 // €19.99
} as const;

/**
 * Límites de planes gratuitos
 */
export const FREE_PLAN_LIMITS = {
  DOCUMENTS: 1,
  MIND_MAPS_TRIAL: 2,
  TESTS_TRIAL: 10,
  FLASHCARDS_TRIAL: 10,
  TOKENS_TRIAL: 50000,
  TRIAL_DAYS: 5
} as const;

/**
 * Factores de riesgo de seguridad
 */
export const SECURITY_RISK_SCORES = {
  MISSING_USER_AGENT: 30,
  BOT_USER_AGENT: 20,
  EXTERNAL_REFERER: 10,
  SUSPICIOUS_PATTERN: 25,
  HIGH_FREQUENCY: 40
} as const;
