'use client';

import { useCallback } from 'react';
import { useBackgroundTasks, BackgroundTask } from '@/contexts/BackgroundTasksContext';

interface GenerationOptions {
  peticion: string;
  contextos?: string[];
  documentos?: any[]; // Documentos completos con storage_path
  cantidad?: number;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

interface PlanEstudiosOptions {
  temarioId: string;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

interface ResumenOptions {
  documento: {
    id: string;
    titulo: string;
    contenido?: string;
    categoria?: string;
    numero_tema?: number;
    // Nuevos campos para Storage
    storage_path?: string;
    file_size_bytes?: number;
    content_type?: string;
    contenido_corto?: string;
  };
  instrucciones: string;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

interface UseBackgroundGenerationReturn {
  generateMapaMental: (options: GenerationOptions) => Promise<string>;
  generateTest: (options: GenerationOptions) => Promise<string>;
  generateFlashcards: (options: GenerationOptions) => Promise<string>;
  generatePlanEstudios: (options: PlanEstudiosOptions) => Promise<string>;
  generateResumen: (options: ResumenOptions) => Promise<string>;
  isGenerating: (type: BackgroundTask['type']) => boolean;
  getActiveTask: (type: BackgroundTask['type']) => BackgroundTask | undefined;
}

export const useBackgroundGeneration = (): UseBackgroundGenerationReturn => {
  const { addTask, updateTask, getTasksByType } = useBackgroundTasks();

  const executeGeneration = useCallback(async (
    type: BackgroundTask['type'],
    action: string,
    options: GenerationOptions
  ): Promise<string> => {
    const { peticion, contextos, documentos, cantidad, onComplete, onError } = options;

    try {
      console.log(`🚀 [FRONTEND] Iniciando generación asíncrona de ${type}...`);

      const requestBody = {
        action,
        peticion,
        contextos,
        documentos,
        cantidad
      };

      const startTime = Date.now();

      // Realizar la petición al API (ahora solo crea la tarea)
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        console.error(`❌ [FRONTEND] Error HTTP para ${type}:`, {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          timestamp: new Date().toISOString()
        });

        const errorData = await response.json().catch(() => ({ error: 'Error desconocido' }));
        const errorMessage = errorData.error || `Error ${response.status}`;

        console.error(`❌ [FRONTEND] Error body para ${type}:`, errorMessage);

        // Si es un error 409 (conflicto), es porque ya hay una tarea del mismo tipo en proceso
        if (response.status === 409) {
          throw new Error(errorMessage);
        }

        throw new Error(`Error en la API: ${response.status} - ${errorMessage}`);
      }

      const responseData = await response.json();
      const { result } = responseData;

      console.log(`✅ [FRONTEND] Tarea ${type} creada en ${responseTime}ms, taskId: ${result.taskId}`);

      // El API ahora devuelve { taskId, status: 'pending' }
      // La tarea aparecerá automáticamente en la UI via Realtime
      // Los callbacks se ejecutarán cuando la tarea se complete via Realtime

      // Crear la tarea local inmediatamente para mostrar en la UI
      addTask({
        type,
        title: peticion.length > 50 ? `${peticion.substring(0, 50)}...` : peticion,
      });

      // Guardar los callbacks para ejecutarlos cuando la tarea se complete
      if (onComplete || onError) {
        // Aquí podrías implementar un sistema de callbacks si es necesario
        // Por ahora, los componentes pueden usar useTaskResults para obtener resultados
        console.log(`📝 [FRONTEND] Callbacks registrados para tarea ${result.taskId}`);
      }

      return result.taskId; // Devolver el ID de la tarea de la base de datos

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      console.error(`❌ [FRONTEND] Error creando tarea ${type}:`, errorMessage);

      // Ejecutar callback de error de forma asíncrona
      if (onError) {
        setTimeout(() => onError(errorMessage), 0);
      }

      throw error;
    }
  }, [addTask]);

  const generateMapaMental = useCallback(async (options: GenerationOptions): Promise<string> => {
    return executeGeneration('mapa-mental', 'generarMapaMental', options);
  }, [executeGeneration]);

  const generateTest = useCallback(async (options: GenerationOptions): Promise<string> => {
    return executeGeneration('test', 'generarTest', options);
  }, [executeGeneration]);

  const generateFlashcards = useCallback(async (options: GenerationOptions): Promise<string> => {
    return executeGeneration('flashcards', 'generarFlashcards', options);
  }, [executeGeneration]);

  const generateResumen = useCallback(async (options: ResumenOptions): Promise<string> => {
    const { documento, instrucciones, onComplete, onError } = options;

    // *** INICIO DE LA SOLUCIÓN ***
    // El problema estaba en que se enviaba el contenido en `contextos` en lugar de
    // enviar el objeto `documento` completo en el campo `documentos`.

    // 1. Preparar la petición para el API.
    const peticion = `${documento.titulo}|${documento.categoria || ''}|${documento.numero_tema || ''}|${instrucciones}`;

    // 2. Llamar a executeGeneration enviando el objeto `documento` en el campo correcto.
    return executeGeneration('resumen', 'generarResumen', {
      peticion,
      documentos: [documento], // <--- ESTA ES LA LÍNEA CORREGIDA
      onComplete,
      onError
    });
    // *** FIN DE LA SOLUCIÓN ***
  }, [executeGeneration]);

  const generatePlanEstudios = useCallback(async (options: PlanEstudiosOptions): Promise<string> => {
    const { temarioId, onComplete, onError } = options;

    // Crear la tarea en segundo plano
    const taskId = addTask({
      type: 'plan-estudios',
      title: 'Generando plan de estudios personalizado',
    });

    try {
      // Marcar como procesando
      updateTask(taskId, { status: 'processing' });

      // Realizar la petición usando la interfaz estándar
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generarPlanEstudios',
          peticion: temarioId, // Usar peticion en lugar de temarioId para consistencia
          contextos: [] // Contextos vacíos para mantener la interfaz estándar
        })
      });

      if (!response.ok) {
        throw new Error(`Error en la API: ${response.status}`);
      }

      const { result } = await response.json();

      // Marcar como completado
      updateTask(taskId, {
        status: 'completed',
        result,
        progress: 100
      });

      // Ejecutar callback de éxito de forma asíncrona para evitar problemas de renderizado
      if (onComplete) {
        setTimeout(() => onComplete(result), 0);
      }

      return taskId;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      // Marcar como error
      updateTask(taskId, {
        status: 'error',
        error: errorMessage
      });

      // Ejecutar callback de error de forma asíncrona para evitar problemas de renderizado
      if (onError) {
        setTimeout(() => onError(errorMessage), 0);
      }

      throw error;
    }
  }, [addTask, updateTask]);

  const isGenerating = useCallback((type: BackgroundTask['type']): boolean => {
    const tasks = getTasksByType(type);
    return tasks.some(task => task.status === 'pending' || task.status === 'processing');
  }, [getTasksByType]);

  const getActiveTask = useCallback((type: BackgroundTask['type']): BackgroundTask | undefined => {
    const tasks = getTasksByType(type);
    return tasks.find(task => task.status === 'pending' || task.status === 'processing');
  }, [getTasksByType]);

  return {
    generateMapaMental,
    generateTest,
    generateFlashcards,
    generatePlanEstudios,
    generateResumen,
    isGenerating,
    getActiveTask,
  };
};
