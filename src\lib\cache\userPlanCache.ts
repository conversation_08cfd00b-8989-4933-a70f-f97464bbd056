// src/lib/cache/userPlanCache.ts
// Sistema de caché centralizado para planes de usuario

interface UserPlanCacheEntry {
  userId: string;
  plan: string;
  timestamp: number;
}

interface FeatureAccessCacheEntry {
  userId: string;
  feature: string;
  hasAccess: boolean;
  timestamp: number;
}

class UserPlanCacheManager {
  private static instance: UserPlanCacheManager;
  private planCache: Map<string, UserPlanCacheEntry> = new Map();
  private featureCache: Map<string, FeatureAccessCacheEntry> = new Map();
  private readonly TTL = 5 * 60 * 1000; // 5 minutos en milisegundos

  private constructor() {}

  public static getInstance(): UserPlanCacheManager {
    if (!UserPlanCacheManager.instance) {
      UserPlanCacheManager.instance = new UserPlanCacheManager();
    }
    return UserPlanCacheManager.instance;
  }

  // Métodos para caché de planes
  public getPlan(userId: string): string | null {
    const entry = this.planCache.get(userId);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > this.TTL) {
      this.planCache.delete(userId);
      return null;
    }

    return entry.plan;
  }

  public setPlan(userId: string, plan: string): void {
    this.planCache.set(userId, {
      userId,
      plan,
      timestamp: Date.now()
    });
  }

  public clearPlan(userId: string): void {
    this.planCache.delete(userId);
  }

  // Métodos para caché de acceso a características
  public getFeatureAccess(userId: string, feature: string): boolean | null {
    const key = `${userId}:${feature}`;
    const entry = this.featureCache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > this.TTL) {
      this.featureCache.delete(key);
      return null;
    }

    return entry.hasAccess;
  }

  public setFeatureAccess(userId: string, feature: string, hasAccess: boolean): void {
    const key = `${userId}:${feature}`;
    this.featureCache.set(key, {
      userId,
      feature,
      hasAccess,
      timestamp: Date.now()
    });
  }

  public clearFeatureAccess(userId: string, feature?: string): void {
    if (feature) {
      const key = `${userId}:${feature}`;
      this.featureCache.delete(key);
    } else {
      // Limpiar todas las características para el usuario
      const keysToDelete: string[] = [];
      this.featureCache.forEach((entry, key) => {
        if (entry.userId === userId) {
          keysToDelete.push(key);
        }
      });
      keysToDelete.forEach(key => this.featureCache.delete(key));
    }
  }

  // Métodos de utilidad
  public clearAllForUser(userId: string): void {
    this.clearPlan(userId);
    this.clearFeatureAccess(userId);
  }

  public clearExpired(): void {
    const now = Date.now();

    // Limpiar planes expirados
    const expiredPlanKeys: string[] = [];
    this.planCache.forEach((entry, key) => {
      if (now - entry.timestamp > this.TTL) {
        expiredPlanKeys.push(key);
      }
    });
    expiredPlanKeys.forEach(key => this.planCache.delete(key));

    // Limpiar características expiradas
    const expiredFeatureKeys: string[] = [];
    this.featureCache.forEach((entry, key) => {
      if (now - entry.timestamp > this.TTL) {
        expiredFeatureKeys.push(key);
      }
    });
    expiredFeatureKeys.forEach(key => this.featureCache.delete(key));
  }

  public getStats(): { planCacheSize: number; featureCacheSize: number } {
    return {
      planCacheSize: this.planCache.size,
      featureCacheSize: this.featureCache.size
    };
  }
}

// Exportar instancia singleton
export const userPlanCache = UserPlanCacheManager.getInstance();

// Limpiar cache expirado cada 10 minutos
if (typeof window !== 'undefined') {
  setInterval(() => {
    userPlanCache.clearExpired();
  }, 10 * 60 * 1000);
}
