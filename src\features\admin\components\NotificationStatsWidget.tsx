'use client';

import React, { useState, useEffect } from 'react';
import { FiMail, FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';
import { toast } from 'react-hot-toast';

export default function NotificationStatsWidget() {
  const [stats, setStats] = useState({ totalFailures: 0, failureRate: 0, errorsByType: {} });
  const [loading, setLoading] = useState(true);
  const [retrying, setRetrying] = useState(false);

  const fetchStats = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/admin/email-failures');
      const data = await res.json();
      if (res.ok) {
        setStats(data.data.failures);
      }
    } catch (error) {
      console.error("Error fetching notification stats:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = async () => {
    setRetrying(true);
    toast.loading('Iniciando reintentos...', { id: 'retry-toast' });
    try {
      const res = await fetch('/api/admin/email-failures', { method: 'POST' });
      const data = await res.json();
      if (res.ok) {
        toast.success(`Reintentos completados: ${data.result.successful} exitosos.`, { id: 'retry-toast' });
        fetchStats(); // Refresh stats after retry
      } else {
        toast.error(data.error || 'Error al reintentar.', { id: 'retry-toast' });
      }
    } catch (error) {
      toast.error('Error de conexión al reintentar.', { id: 'retry-toast' });
    } finally {
      setRetrying(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex items-center mb-4">
        <FiMail className="w-6 h-6 text-gray-500 mr-3" />
        <h3 className="text-lg font-semibold text-gray-800">Salud de Notificaciones</h3>
      </div>
      {loading ? (
        <p>Cargando estadísticas...</p>
      ) : (
        <div className="space-y-4">
          <div className="flex justify-around text-center">
            <div>
              <p className="text-2xl font-bold text-red-600">{stats.totalFailures}</p>
              <p className="text-sm text-gray-500">Fallos Totales</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-red-600">{stats.failureRate}%</p>
              <p className="text-sm text-gray-500">Tasa de Fallo</p>
            </div>
          </div>
          <button
            onClick={handleRetry}
            disabled={retrying}
            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${retrying ? 'animate-spin' : ''}`} />
            {retrying ? 'Reintentando...' : 'Reintentar Envíos Fallidos'}
          </button>
        </div>
      )}
    </div>
  );
}
