// src/lib/stripe/plans.ts
// Configuración de planes integrada con sistema de límites

import { PLAN_CONFIGURATIONS } from '@/config';

export const PLANS = {
  free: {
    id: 'free',
    name: 'Plan Gratis',
    price: 0,
    stripeProductId: null,
    stripePriceId: null,
    features: [
      'Incluye:',
      '• Uso de la plataforma solo durante 5 días',
      '• Subida de documentos: máximo 1 documento',
      '• Generador de test: máximo 10 preguntas test',
      '• Generador de flashcards: máximo 10 tarjetas flashcard',
      '• Generador de mapas mentales: máximo 2 mapas mentales',
      'No incluye:',
      '• Planificación de estudios',
      '• Habla con tu preparador IA',
      '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1',
    ],
    limits: PLAN_CONFIGURATIONS.free.limits,
    planConfig: PLAN_CONFIGURATIONS.free
  },
  usuario: {
    id: 'usuario',
    name: 'Plan Usuario',
    price: 1000, // En centavos (€10.00)
    stripeProductId: 'prod_SR65BdKdek1OXd',
    // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe
    // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard
    stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',
    features: [
      'Incluye:',
      '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',
      '• Subida de documentos',
      '• Habla con tu preparador IA *',
      '• Generador de test *',
      '• Generador de flashcards *',
      '• Generador de mapas mentales *',
      '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 500.000 tokens.',
      'No incluye:',
      '• Planificación de estudios',
      '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1',
    ],
    limits: PLAN_CONFIGURATIONS.usuario.limits,
    planConfig: PLAN_CONFIGURATIONS.usuario
  },
  pro: {
    id: 'pro',
    name: 'Plan Pro',
    price: 1500, // En centavos (€15.00)
    stripeProductId: 'prod_SR66U2G7bVJqu3',
    stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',
    features: [
      'Incluye:',
      '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',
      '• Subida de documentos',
      '• Planificación de estudios mediante IA*',
      '• Habla con tu preparador IA *',
      '• Generador de test *',
      '• Generador de flashcards *',
      '• Generador de mapas mentales *',
      '• Generación de resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1',
      '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens.',
    ],
    limits: PLAN_CONFIGURATIONS.pro.limits,
    planConfig: PLAN_CONFIGURATIONS.pro
  }
} as const;

export type PlanId = keyof typeof PLANS;

// Función para obtener plan por ID
export function getPlanById(planId: string): typeof PLANS[PlanId] | null {
  return PLANS[planId as PlanId] || null;
}

// Función para validar si un plan es válido
export function isValidPlan(planId: string): planId is PlanId {
  return planId in PLANS;
}

// Función para obtener configuración completa del plan
export function getFullPlanConfig(planId: string) {
  const plan = getPlanById(planId);
  return plan?.planConfig || null;
}

// Configuración de productos adicionales
export const ADDITIONAL_PRODUCTS = {
  tokens: {
    id: 'tokens',
    name: 'Tokens Adicionales',
    description: '1,000,000 tokens adicionales para tu cuenta',
    price: 1000, // En centavos (€10.00)
    tokenAmount: 1000000,
    // Estos IDs se deben crear en Stripe Dashboard
    stripeProductId: 'prod_tokens_additional', // Placeholder - crear en Stripe
    stripePriceId: 'price_tokens_additional', // Placeholder - crear en Stripe
  }
} as const;

// URLs de la aplicación
export const APP_URLS = {
  success: `${process.env.NEXT_PUBLIC_APP_URL}/thank-you`,
  cancel: `${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan`,
  webhook: `${process.env.NEXT_PUBLIC_APP_URL}/api/stripe/webhook`,
} as const;
