// src/hooks/useAccountStatus.ts
// Hook React para consumir el estado de la cuenta de forma reactiva

'use client';

import { useState, useEffect, useCallback } from 'react';
import { getAccountStatus, AccountStatusResult } from '@/lib/services/accountStatusService';
import { useAuth } from '@/contexts/AuthContext';

export function useAccountStatus() {
  const [statusInfo, setStatusInfo] = useState<AccountStatusResult>({
    status: 'unauthenticated',
    plan: 'none',
    userId: null,
    isDegraded: false,
    isAuthenticated: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { user, isLoading: authLoading } = useAuth();

  const fetchStatus = useCallback(async () => {
    if (authLoading) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await getAccountStatus();
      setStatusInfo(result);
    } catch (err) {
      console.error('Error fetching account status:', err);
      setError('Error al obtener el estado de la cuenta');
      // Fallback seguro
      setStatusInfo({
        status: 'unauthenticated',
        plan: 'none',
        userId: null,
        isDegraded: false,
        isAuthenticated: false
      });
    } finally {
      setIsLoading(false);
    }
  }, [authLoading]);

  useEffect(() => {
    fetchStatus();
  }, [user, fetchStatus]); // Se ejecuta cuando el usuario cambia

  // Funciones de utilidad derivadas del estado
  const isBlocked = statusInfo.status === 'degraded_from_paid';
  const isPaidUser = statusInfo.status === 'active_paid';
  const isFreeUser = statusInfo.status === 'active_free';
  const isAuthenticated = statusInfo.isAuthenticated;
  const needsUpgrade = isBlocked;

  return {
    // Estado principal
    ...statusInfo,
    isLoading,
    error,
    
    // Funciones de utilidad
    isBlocked,
    isPaidUser,
    isFreeUser,
    isAuthenticated,
    needsUpgrade,
    
    // Función para refrescar manualmente
    refreshStatus: fetchStatus
  };
}
