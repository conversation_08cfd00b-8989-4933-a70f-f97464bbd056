/**
 * Tipos compartidos entre diferentes features de la aplicación
 *
 * Este archivo contiene interfaces y tipos que son utilizados
 * por múltiples features o que representan conceptos transversales.
 */

import { ReactNode } from 'react';
import { DificultadRespuesta } from './database';

// ============================================================================
// FLASHCARDS - Tipos migrados desde src/features/flashcards/components/types.ts
// ============================================================================

// Tipos básicos
export type EstadoFlashcard = 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendido';

export interface ProgresoFlashcard {
  estado: EstadoFlashcard;
  siguienteRepaso: string;
  factorFacilidad: number;
  repeticiones: number;
  intervalo: number;
}

export interface EstadisticasColeccion {
  total: number;
  nuevas: number;
  aprendiendo: number;
  repasando: number;
  aprendidas: number;
  paraHoy: number;
}

// Tipos para opciones de estudio
export type TipoEstudio = 'programadas' | 'dificiles' | 'aleatorias' | 'no-recientes' | 'nuevas' | 'aprendiendo' | 'repasando' | 'aprendidas';

export interface OpcionEstudio {
  tipo: TipoEstudio;
  label: string;
  descripcion: string;
  icon: ReactNode;
  color: string;
  bgColor: string;
  hoverBgColor: string;
}

// Tipos para botones de dificultad
export interface DificultadButton {
  tipo: DificultadRespuesta;
  label: string;
  color: string;
  bgColor: string;
  hoverBgColor: string;
  icon: ReactNode;
}

// ============================================================================
// TODO: Consolidar tipos inline de otros features
// ============================================================================

// TODO: Tipos de planificación
// TODO: Tipos de tests
// TODO: Tipos de conversaciones
// TODO: Tipos de documentos
