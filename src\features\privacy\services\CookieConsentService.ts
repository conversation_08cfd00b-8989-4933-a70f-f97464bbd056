// src/features/privacy/services/CookieConsentService.ts
// Service Layer Pattern para lógica de negocio de cookies

import { CookieConsent, CookiePreferences, CookieConsentState } from '../types/cookie.types';
import { CookieStorageRepository } from './CookieStorageRepository';
import { COOKIE_CONSENT_VERSION, DEFAULT_COOKIE_PREFERENCES } from '../constants/cookie.constants';

export class CookieConsentService {
  constructor(private repository: CookieStorageRepository) {}

  async getConsentState(): Promise<CookieConsentState> {
    const consent = await this.repository.getConsent();
    const preferences = await this.repository.getPreferences();

    return {
      hasConsent: consent?.granted ?? false,
      preferences,
      lastUpdated: consent?.timestamp ?? null,
    };
  }

  async grantConsent(preferences?: CookiePreferences): Promise<void> {
    const finalPreferences = preferences || DEFAULT_COOKIE_PREFERENCES;
    
    const consent: CookieConsent = {
      granted: true,
      timestamp: new Date(),
      version: COOKIE_CONSENT_VERSION,
    };

    await this.repository.setConsent(consent);
    await this.repository.setPreferences(finalPreferences);

    // Log para observabilidad siguiendo patrones existentes
    if (typeof window !== 'undefined') {
      console.info('Cookie consent granted', {
        consentType: 'implicit',
        timestamp: consent.timestamp.toISOString(),
        preferences: finalPreferences,
      });
    }
  }

  async revokeConsent(): Promise<void> {
    await this.repository.clearConsent();
    
    if (typeof window !== 'undefined') {
      console.info('Cookie consent revoked', {
        timestamp: new Date().toISOString(),
      });
    }
  }

  async updatePreferences(preferences: CookiePreferences): Promise<void> {
    await this.repository.setPreferences(preferences);
    
    if (typeof window !== 'undefined') {
      console.info('Cookie preferences updated', {
        preferences,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async hasValidConsent(): Promise<boolean> {
    const consent = await this.repository.getConsent();
    
    if (!consent || !consent.granted) {
      return false;
    }

    // Verificar si el consentimiento es de la versión actual
    return consent.version === COOKIE_CONSENT_VERSION;
  }

  async shouldShowBanner(): Promise<boolean> {
    return !(await this.hasValidConsent());
  }
}
