import { useEffect, useRef, useCallback } from 'react';

interface UseInactivityTimerProps {
  timeout: number; // Tiempo en milisegundos
  onTimeout: () => void; // Función a ejecutar cuando se agote el tiempo
  enabled?: boolean; // Si el timer está habilitado
}

// Eventos que consideramos como actividad del usuario (definido fuera del hook para estabilidad)
const ACTIVITY_EVENTS = [
  'mousedown',
  'mousemove',
  'keypress',
  'scroll',
  'touchstart',
  'click'
] as const;

/**
 * Hook para manejar la desconexión automática por inactividad
 */
export const useInactivityTimer = ({
  timeout,
  onTimeout,
  enabled = true
}: UseInactivityTimerProps) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());

  // Obtener tareas de forma segura
  let backgroundTasks;
  try {
    backgroundTasks = useBackgroundTasks();
  } catch (error) {
    // Si el provider no está disponible, usar valores por defecto
    backgroundTasks = { tasks: [] };
  }

  // Función para obtener tareas activas
  const getActiveTasks = useCallback(() => {
    return (backgroundTasks.tasks || []).filter(task =>
      task.status === 'pending' || task.status === 'processing'
    );
  }, [backgroundTasks.tasks]);

  // Función para resetear el timer
  const resetTimer = useCallback(() => {
    if (!enabled) return;

    // Limpiar el timer anterior si existe
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Actualizar la última actividad
    lastActivityRef.current = Date.now();

    // Siempre crear un nuevo timer, independientemente de las tareas de IA
    // La actividad del usuario siempre reinicia el timer
    timeoutRef.current = setTimeout(() => {
      // Solo ejecutar logout si no hay tareas de IA activas en el momento del timeout
      const currentTasks = getActiveTasks();

      if (currentTasks.length === 0) {
        onTimeout();
      } else {
        // Reintentar en 1 minuto si hay tareas activas
        setTimeout(() => resetTimer(), 60000);
      }
    }, timeout);
  }, [timeout, onTimeout, enabled, getActiveTasks]);

  // Función para limpiar el timer
  const clearTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Función para obtener el tiempo restante
  const getTimeRemaining = useCallback(() => {
    if (!enabled || !timeoutRef.current) return 0;
    
    const elapsed = Date.now() - lastActivityRef.current;
    const remaining = Math.max(0, timeout - elapsed);
    return remaining;
  }, [timeout, enabled]);

  useEffect(() => {
    if (!enabled) {
      clearTimer();
      return;
    }

    // Función que maneja los eventos de actividad
    const handleActivity = () => {
      resetTimer();
    };

    // Agregar event listeners
    ACTIVITY_EVENTS.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Iniciar el timer
    resetTimer();

    // Cleanup
    return () => {
      ACTIVITY_EVENTS.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
      clearTimer();
    };
  }, [enabled, resetTimer, clearTimer]);

  return {
    resetTimer,
    clearTimer,
    getTimeRemaining
  };
};

/**
 * Hook simplificado para desconexión automática
 */
export const useAutoLogout = (
  timeoutMinutes: number = 5,
  onLogout: () => void,
  enabled: boolean = true
) => {
  const timeoutMs = timeoutMinutes * 60 * 1000; // Convertir minutos a milisegundos
  
  return useInactivityTimer({
    timeout: timeoutMs,
    onTimeout: onLogout,
    enabled
  });
};
