// src/lib/services/planValidation.ts
// Servicio para validación de planes y acceso a características

import { SupabaseAdminService, ExtendedUserProfile } from '@/lib/supabase/admin';
import {
  getPlanConfiguration,
  hasFeatureAccess,
  canPerformAction,
  ACTION_TO_FEATURE_MAP,
  type ActionType,
  type FeatureId,
  ERROR_MESSAGES,
  TOKEN_LIMITS
} from '@/config';
import { FreeAccountService } from './freeAccountService';
import { WebhookLogger } from '@/lib/utils/webhookLogger';

export interface ValidationResult {
  allowed: boolean;
  reason?: string;
  remainingUsage?: number;
  planLimits?: any;
}

export interface UserAccessInfo {
  userId: string;
  plan: string;
  paymentVerified: boolean;
  features: string[];
  limits: any;
  currentUsage: any;
}

export class PlanValidationService {
  
  /**
   * Validar acceso de usuario a una característica específica
   */
  static async validateFeatureAccess(
    userId: string,
    featureName: string,
    tokensToUse: number = 0
  ): Promise<ValidationResult> {
    try {

      // Obtener perfil del usuario
      const profile = await SupabaseAdminService.getUserProfile(userId);

      if (!profile) {
        await WebhookLogger.logFeatureAccess(
          userId,
          featureName,
          false,
          'unknown',
          0,
          'User profile not found'
        );

        return {
          allowed: false,
          reason: ERROR_MESSAGES.PROFILE_NOT_FOUND
        };
      }
      
      // Verificar pago para planes de pago
      if (profile.subscription_plan !== 'free' && !profile.payment_verified) {
        await WebhookLogger.logFeatureAccess(
          userId,
          featureName,
          false,
          profile.subscription_plan,
          0,
          'Payment not verified'
        );
        
        return {
          allowed: false,
          reason: 'Pago no verificado. Complete el proceso de pago para acceder a esta característica.'
        };
      }
      
      // Verificar acceso a la característica según el plan
      const hasAccess = hasFeatureAccess(profile.subscription_plan, featureName);

      if (!hasAccess) {

        await WebhookLogger.logFeatureAccess(
          userId,
          featureName,
          false,
          profile.subscription_plan,
          0,
          `Feature not available in ${profile.subscription_plan} plan`
        );

        return {
          allowed: false,
          reason: `La característica ${featureName} no está disponible en su plan ${profile.subscription_plan}`
        };
      }

      // Verificar límites de tokens si aplica
      if (tokensToUse > 0) {
        const tokenValidation = await this.validateTokenUsage(profile, tokensToUse);

        if (!tokenValidation.allowed) {

          await WebhookLogger.logFeatureAccess(
            userId,
            featureName,
            false,
            profile.subscription_plan,
            tokensToUse,
            tokenValidation.reason
          );

          return tokenValidation;
        }
      }
      
      // Log de acceso exitoso
      await WebhookLogger.logFeatureAccess(
        userId,
        featureName,
        true,
        profile.subscription_plan,
        tokensToUse
      );
      
      return {
        allowed: true,
        remainingUsage: profile.monthly_token_limit - profile.current_month_tokens,
        planLimits: {
          monthlyTokens: profile.monthly_token_limit,
          currentTokens: profile.current_month_tokens
        }
      };
      
    } catch (error) {
      console.error('Error validating feature access:', error);
      
      await WebhookLogger.logFeatureAccess(
        userId,
        featureName,
        false,
        'error',
        tokensToUse,
        'Internal validation error'
      );
      
      return {
        allowed: false,
        reason: 'Error interno de validación'
      };
    }
  }
  
  /**
   * Validar uso de tokens
   */
  private static async validateTokenUsage(
    profile: ExtendedUserProfile,
    tokensToUse: number
  ): Promise<ValidationResult> {

    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';

    // Reset de tokens si es un nuevo mes
    if (profile.current_month !== currentMonth) {
      await SupabaseAdminService.upsertUserProfile({
        ...profile,
        current_month_tokens: 0,
        current_month: currentMonth,
        updated_at: new Date().toISOString()
      });

      profile.current_month_tokens = 0;
      profile.current_month = currentMonth;
    }

    // Verificar si tiene tokens suficientes
    const tokensAfterUse = profile.current_month_tokens + tokensToUse;



    if (tokensAfterUse > profile.monthly_token_limit) {


      return {
        allowed: false,
        reason: `Límite mensual de tokens alcanzado. Usado: ${profile.current_month_tokens}/${profile.monthly_token_limit}`,
        remainingUsage: Math.max(0, profile.monthly_token_limit - profile.current_month_tokens)
      };
    }



    return {
      allowed: true,
      remainingUsage: profile.monthly_token_limit - tokensAfterUse
    };
  }
  
  /**
   * Obtener información completa de acceso del usuario
   */
  static async getUserAccessInfo(userId: string): Promise<UserAccessInfo | null> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return null;
      }
      
      const planConfig = getPlanConfiguration(profile.subscription_plan);
      
      if (!planConfig) {
        return null;
      }
      
      // Obtener uso actual de características
      let currentUsage = {
        tokens: profile.current_month_tokens,
        tokenLimit: profile.monthly_token_limit,
        month: profile.current_month,
        documents: 0,
        tests: 0,
        flashcards: 0,
        mindMaps: 0
      };

      // Obtener conteo real de documentos para todos los usuarios
      try {
        const documentsCount = await SupabaseAdminService.getDocumentsCount(userId);
        currentUsage.documents = documentsCount;
      } catch (error) {
        console.error('Error getting documents count:', error);
      }

      // Si es plan gratuito, obtener uso actual de FreeAccountService
      if (profile.subscription_plan === 'free') {
        try {
          const { FreeAccountService } = await import('./freeAccountService');
          const status = await FreeAccountService.getFreeAccountStatus(userId);
          if (status) {
            currentUsage = {
              ...currentUsage,
              tests: status.usageCount.tests || 0,
              flashcards: status.usageCount.flashcards || 0,
              mindMaps: status.usageCount.mindMaps || 0
            };
          }
        } catch (error) {
          console.error('Error getting free account usage:', error);
        }
      }

      // Mapear límites a las claves esperadas por el frontend
      const mappedLimits = {
        // Mantener las claves originales primero
        ...planConfig.limits,
        // Luego mapear a las claves esperadas por el frontend
        tests: planConfig.limits.testsPerWeek ?? 0,
        flashcards: planConfig.limits.flashcardsPerWeek ?? 0,
        mindMaps: planConfig.limits.mindMapsPerWeek ?? 0
      };

      return {
        userId,
        plan: profile.subscription_plan,
        paymentVerified: profile.payment_verified,
        features: planConfig.features || [],
        limits: mappedLimits,
        currentUsage
      };
      
    } catch (error) {
      console.error('Error getting user access info:', error);
      return null;
    }
  }
  
  /**
   * Verificar si el usuario puede realizar una acción específica
   */
  static async canUserPerformAction(
    userId: string,
    action: ActionType,
    quantity: number = 1
  ): Promise<ValidationResult> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return {
          allowed: false,
          reason: ERROR_MESSAGES.USER_NOT_FOUND
        };
      }
      
      // Mapear acciones a características usando configuración centralizada
      const featureName = ACTION_TO_FEATURE_MAP[action];

      // Verificar acceso básico a la característica
      const basicValidation = await this.validateFeatureAccess(userId, featureName, quantity);
      
      if (!basicValidation.allowed) {
        return basicValidation;
      }
      
      // Para plan gratuito, verificar límites específicos usando FreeAccountService
      if (profile.subscription_plan === 'free') {
        const featureMap = {
          'test_generation': 'tests',
          'flashcard_generation': 'flashcards',
          'mind_map_generation': 'mindMaps'
        } as const;

        const featureKey = featureMap[action as keyof typeof featureMap];

        if (featureKey) {
          const canPerform = await FreeAccountService.canPerformAction(userId, featureKey, quantity);

          if (!canPerform.allowed) {
            return {
              allowed: false,
              reason: canPerform.reason || `Límite alcanzado para ${action}`,
              remainingUsage: canPerform.remaining
            };
          }
        }
      }
      
      return {
        allowed: true,
        remainingUsage: basicValidation.remainingUsage
      };
      
    } catch (error) {
      console.error('Error checking user action:', error);
      return {
        allowed: false,
        reason: 'Error interno de validación'
      };
    }
  }
  
  /**
   * Actualizar uso de tokens después de una operación
   */
  static async updateTokenUsage(
    userId: string,
    tokensUsed: number,
    activity: string
  ): Promise<boolean> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return false;
      }
      
      const newTokenCount = profile.current_month_tokens + tokensUsed;
      
      await SupabaseAdminService.upsertUserProfile({
        ...profile,
        current_month_tokens: newTokenCount,
        updated_at: new Date().toISOString()
      });
      

      
      return true;
      
    } catch (error) {
      console.error('Error updating token usage:', error);
      return false;
    }
  }
  
  /**
   * Verificar si el usuario necesita upgrade de plan
   */
  static async checkUpgradeNeeded(userId: string): Promise<{
    needsUpgrade: boolean;
    reason?: string;
    suggestedPlan?: string;
  }> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return { needsUpgrade: false };
      }
      
      // Verificar si está cerca del límite de tokens
      const usagePercentage = (profile.current_month_tokens / profile.monthly_token_limit) * 100;
      
      if (usagePercentage >= TOKEN_LIMITS.CRITICAL_THRESHOLD_PERCENTAGE) {
        const suggestedPlan = profile.subscription_plan === 'free' ? 'usuario' : 'pro';
        
        return {
          needsUpgrade: true,
          reason: `Has usado el ${usagePercentage.toFixed(1)}% de tus tokens mensuales`,
          suggestedPlan
        };
      }
      
      return { needsUpgrade: false };
      
    } catch (error) {
      console.error('Error checking upgrade need:', error);
      return { needsUpgrade: false };
    }
  }
}
