'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FiGrid, FiUserCheck, FiMail, FiBarChart2 } from 'react-icons/fi';

const menuItems = [
  { href: '/admin', label: 'Dashboard', icon: FiGrid },
  { href: '/admin/reactivate', label: 'Reactivar Usuario', icon: FiUserCheck },
];

export default function AdminSidebar() {
  const pathname = usePathname();

  return (
    <aside className="w-64 bg-white shadow-md h-screen sticky top-0">
      <div className="p-4">
        <h2 className="text-xl font-bold text-gray-800">Admin Panel</h2>
      </div>
      <nav className="mt-6">
        <ul>
          {menuItems.map((item) => (
            <li key={item.href}>
              <Link
                href={item.href}
                className={`flex items-center px-4 py-3 my-1 transition-colors duration-200 
                  ${pathname === item.href
                    ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-500'
                    : 'text-gray-600 hover:bg-gray-100'
                  }`}
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}
