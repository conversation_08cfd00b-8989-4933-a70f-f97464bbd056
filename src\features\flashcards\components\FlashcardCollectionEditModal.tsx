import React, { useState, useEffect } from 'react';
import { FiX, FiSave } from 'react-icons/fi';
import { ColeccionFlashcards } from '@/lib/supabase/supabaseClient';
import { actualizarColeccionFlashcards } from '@/lib/supabase/flashcardsService';
import { toast } from 'react-hot-toast';

interface FlashcardCollectionEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  coleccion: ColeccionFlashcards;
  onSave: (coleccionActualizada: ColeccionFlashcards) => void;
}

const FlashcardCollectionEditModal: React.FC<FlashcardCollectionEditModalProps> = ({
  isOpen,
  onClose,
  coleccion,
  onSave
}) => {
  const [titulo, setTitulo] = useState('');
  const [descripcion, setDescripcion] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Cargar datos de la colección cuando se abre el modal
  useEffect(() => {
    if (isOpen && coleccion) {
      setTitulo(coleccion.titulo);
      setDescripcion(coleccion.descripcion || '');
    }
  }, [isOpen, coleccion]);

  const handleSave = async () => {
    if (!titulo.trim()) {
      toast.error('El título de la colección es obligatorio');
      return;
    }

    setIsLoading(true);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Actualizando colección...');

      const coleccionActualizada = await actualizarColeccionFlashcards(
        coleccion.id,
        titulo.trim(),
        descripcion.trim() || undefined
      );

      if (coleccionActualizada) {
        toast.success('Colección actualizada correctamente', { id: loadingToastId });
        onSave(coleccionActualizada);
        onClose();
      } else {
        toast.error('Error al actualizar la colección', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al actualizar colección:', error);
      toast.error('Error inesperado al actualizar la colección', { id: loadingToastId });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setTitulo(coleccion.titulo);
    setDescripcion(coleccion.descripcion || '');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Editar Colección
          </h2>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isLoading}
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Body */}
        <div className="p-6 space-y-4">
          {/* Título */}
          <div>
            <label htmlFor="titulo" className="block text-sm font-medium text-gray-700 mb-2">
              Título *
            </label>
            <input
              id="titulo"
              type="text"
              value={titulo}
              onChange={(e) => setTitulo(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Ingresa el título de la colección"
              disabled={isLoading}
              maxLength={200}
            />
          </div>

          {/* Descripción */}
          <div>
            <label htmlFor="descripcion" className="block text-sm font-medium text-gray-700 mb-2">
              Descripción
            </label>
            <textarea
              id="descripcion"
              value={descripcion}
              onChange={(e) => setDescripcion(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="Descripción opcional de la colección"
              disabled={isLoading}
              maxLength={500}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            disabled={isLoading || !titulo.trim()}
          >
            <FiSave size={16} />
            <span>{isLoading ? 'Guardando...' : 'Guardar'}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default FlashcardCollectionEditModal;
