import { prepararDocumentos } from './geminiClient';
import { PROMPT_MAPAS_MENTALES } from '@/config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';
import { type Chunk } from '@/lib/utils/textProcessing';
import { seleccionarChunksRelevantes } from '@/lib/utils/chunkSelector';
import * as Sentry from "@sentry/nextjs";

/**
 * Genera un mapa mental a partir de los documentos
 */
export async function generarMapaMental(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  instrucciones?: string
): Promise<any> {
  try {
    // Validar entrada
    if (!documentos || documentos.length === 0) {
      throw new Error("No se han proporcionado documentos para generar el mapa mental.");
    }

    // Preparar el contenido de los documentos
    const resultadoDocumentos = prepararDocumentos(documentos);

    // Verificar si se usó chunking
    if (resultadoDocumentos.wasChunked && Array.isArray(resultadoDocumentos.content)) {
      return await procesarMapaMentalConChunks(resultadoDocumentos.content, instrucciones, documentos);
    } else {
      // Procesamiento tradicional para documentos sin chunking
      const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
        ? resultadoDocumentos.content.join('\n\n')
        : resultadoDocumentos.content;

      if (!contenidoDocumentos || contenidoDocumentos.trim().length === 0) {
        throw new Error("El contenido de los documentos está vacío o no es válido.");
      }

      return await procesarMapaMentalSinChunks(contenidoDocumentos, instrucciones);
    }

  } catch (error) {
    console.error('Error al generar mapa mental:', error);
    throw error;
  }
}

/**
 * Procesa mapa mental con chunking - selecciona chunks relevantes y los combina en un solo contexto
 */
async function procesarMapaMentalConChunks(
  chunks: Chunk[],
  instrucciones?: string,
  documentos?: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<any> {
  // Selección inteligente de chunks relevantes
  const instruccionesLimpias = instrucciones?.trim() || 'Crea un mapa mental que organice los conceptos principales del contenido.';

  const selectionResult = seleccionarChunksRelevantes(chunks, instruccionesLimpias);

  if (selectionResult.selectedChunks.length === 0) {
    selectionResult.selectedChunks = chunks.slice(0, 3); // Máximo 3 chunks como fallback
  }


  // PASO 2 (NUEVO): Combinar el contenido de los chunks seleccionados en un solo contexto
  const combinedContent = selectionResult.selectedChunks
    .map(chunk => chunk.content)
    .join('\n\n---\n\n'); // Un separador claro ayuda a la IA

  // PASO 3: Preparar el prompt para una ÚNICA llamada
  let finalPrompt = PROMPT_MAPAS_MENTALES.replace('{documentos}', combinedContent);
  finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);

  // PASO 4: Hacer UNA SOLA llamada a la IA
  const config = getOpenAIConfig('MAPAS_MENTALES');
  const responseText = await llamarOpenAI([{ role: 'user' as const, content: finalPrompt }], {
    ...config,
    activityName: `Generación [Combinada] de Mapa Mental`
  });

  // PASO 5: Parsear la respuesta directamente (ya no se necesita combinar)
  // Validar que la respuesta no esté vacía
  if (!responseText || responseText.trim().length === 0) {
    throw new Error("La IA no generó ningún contenido para el mapa mental.");
  }

  // Extraer y limpiar la respuesta
  let htmlContent = responseText.trim();

  // Buscar el HTML en la respuesta (puede estar envuelto en markdown)
  const htmlMatch = htmlContent.match(/<!DOCTYPE html>[\s\S]*<\/html>/i);
  if (htmlMatch) {
    htmlContent = htmlMatch[0];
  }

  // Limpiar marcadores de código markdown si existen
  htmlContent = htmlContent
    .replace(/```html/gi, '')
    .replace(/```/g, '')
    .trim();

  if (!htmlContent || htmlContent.length === 0) {
    throw new Error("No se pudo extraer contenido HTML válido del mapa mental generado.");
  }
  return htmlContent;
}

/**
 * Procesa mapa mental sin chunking - método tradicional
 */
async function procesarMapaMentalSinChunks(
  contenidoDocumentos: string,
  instrucciones?: string
): Promise<any> {
  // Validar y limpiar instrucciones
  const instruccionesLimpias = instrucciones?.trim() || 'Crea un mapa mental que organice los conceptos principales del contenido.';

  // Construir el prompt final con validación
  let finalPrompt = PROMPT_MAPAS_MENTALES.replace('{documentos}', contenidoDocumentos);
  finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);

  // Obtener configuración específica para mapas mentales
  const config = getOpenAIConfig('MAPAS_MENTALES');


  // Generar el mapa mental usando OpenAI con la configuración correcta
  const messages = [{ role: 'user' as const, content: finalPrompt }];
  const responseText = await llamarOpenAI(messages, {
    ...config,
    activityName: 'Generación de Mapa Mental'
  });

  // Validar que la respuesta no esté vacía
  if (!responseText || responseText.trim().length === 0) {
    throw new Error("La IA no generó ningún contenido para el mapa mental.");
  }

  // Extraer y limpiar la respuesta (sin validaciones restrictivas)
  let htmlContent = responseText.trim();

  // Buscar el HTML en la respuesta (puede estar envuelto en markdown)
  const htmlMatch = htmlContent.match(/<!DOCTYPE html>[\s\S]*<\/html>/i);
  if (htmlMatch) {
    htmlContent = htmlMatch[0];
  }

  // Limpiar marcadores de código markdown si existen
  htmlContent = htmlContent
    .replace(/```html/gi, '')
    .replace(/```/g, '')
    .trim();

  

  // Validar que el HTML sea válido
  if (!htmlContent.includes('<!DOCTYPE html>') || !htmlContent.includes('</html>')) {
    console.warn('⚠️ El contenido generado no parece ser HTML válido');
    console.log('Contenido completo:', htmlContent);
  } else {
    console.log('✅ HTML válido detectado');
  }

  // Retornar el contenido tal como lo generó la IA (sin validaciones)
  return htmlContent;
}
