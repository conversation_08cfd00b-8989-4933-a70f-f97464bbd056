// src/app/api/debug/manual-activation/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/config';
import { StripeWebhookHandlers } from '@/lib/services/stripeWebhookHandlers';

export async function POST(request: NextRequest) {
  try {
    const { sessionId } = await request.json();

    if (!sessionId) {
      return NextResponse.json({
        error: 'Session ID es requerido'
      }, { status: 400 });
    }


    // 1. Obtener la sesión de Stripe
    if (!stripe) {
      return NextResponse.json({
        error: 'Stripe no configurado'
      }, { status: 500 });
    }

    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    if (!session) {
      return NextResponse.json({
        error: 'Sesión no encontrada en Stripe'
      }, { status: 404 });
    }

    // 2. Simular el webhook manualmente
    if (session.payment_status === 'paid') {      
      const result = await StripeWebhookHandlers.handleCheckoutSessionCompleted(session);
      
      return NextResponse.json({
        success: true,
        message: 'Activación manual completada',
        webhookResult: result,
        sessionData: {
          id: session.id,
          payment_status: session.payment_status,
          customer_email: session.customer_details?.email,
          metadata: session.metadata
        }
      });
    } else {
      return NextResponse.json({
        error: 'El pago no está completado',
        payment_status: session.payment_status
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error en activación manual:', error);
    return NextResponse.json({
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
