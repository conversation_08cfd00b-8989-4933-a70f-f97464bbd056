/**
 * Monitor de Rendimiento del Sistema de Chunking
 * 
 * Este servicio monitorea y registra métricas de rendimiento del sistema de chunking
 * para identificar optimizaciones y detectar problemas de performance.
 * 
 * IMPLEMENTADO en Fase 5 - Optimización Final
 */

export interface ChunkingPerformanceMetrics {
  /** ID único de la sesión de chunking */
  sessionId: string;
  
  /** Timestamp de inicio del procesamiento */
  startTime: number;
  
  /** Timestamp de finalización del procesamiento */
  endTime?: number;
  
  /** Duración total del procesamiento en ms */
  totalDurationMs?: number;
  
  /** Tamaño del documento original en caracteres */
  originalDocumentSize: number;
  
  /** Número de chunks generados */
  chunksGenerated: number;
  
  /** Número de secciones detectadas */
  sectionsDetected: number;
  
  /** Tamaño promedio de chunks */
  averageChunkSize: number;
  
  /** Ratio de compresión (chunks/secciones) */
  compressionRatio: number;
  
  /** Tipo de contenido procesado */
  contentType: string;
  
  /** Configuración utilizada */
  configUsed: {
    maxChunkSize: number;
    minChunkSize: number;
    overlapSize: number;
  };
  
  /** Métricas de eficiencia */
  efficiency: {
    /** Chunks por segundo */
    chunksPerSecond: number;
    
    /** Caracteres procesados por segundo */
    charactersPerSecond: number;
    
    /** Tiempo promedio por chunk */
    averageTimePerChunk: number;
    
    /** Reducción vs chunking naive (%) */
    reductionVsNaive: number;
  };
  
  /** Errores o advertencias */
  issues?: Array<{
    type: 'warning' | 'error';
    message: string;
    timestamp: number;
  }>;
}

export interface PerformanceAlert {
  level: 'info' | 'warning' | 'critical';
  message: string;
  metrics: Partial<ChunkingPerformanceMetrics>;
  timestamp: number;
  suggestions?: string[];
}

/**
 * Servicio de monitoreo de rendimiento del chunking
 */
export class ChunkingPerformanceMonitor {
  private static instance: ChunkingPerformanceMonitor;
  private activeSessions = new Map<string, ChunkingPerformanceMetrics>();
  private historicalMetrics: ChunkingPerformanceMetrics[] = [];
  private alertCallbacks: Array<(alert: PerformanceAlert) => void> = [];
  
  // Umbrales de rendimiento
  private readonly PERFORMANCE_THRESHOLDS = {
    MAX_PROCESSING_TIME_MS: 60000,      // 1 minuto máximo
    MAX_CHUNKS_PER_DOCUMENT: 25,        // Máximo 25 chunks
    MIN_COMPRESSION_RATIO: 0.3,         // Mínimo 30% de compresión
    MIN_CHARACTERS_PER_SECOND: 1000,    // Mínimo 1k caracteres/segundo
    MAX_AVERAGE_TIME_PER_CHUNK: 3000,   // Máximo 3 segundos por chunk
  };
  
  private constructor() {}
  
  public static getInstance(): ChunkingPerformanceMonitor {
    if (!ChunkingPerformanceMonitor.instance) {
      ChunkingPerformanceMonitor.instance = new ChunkingPerformanceMonitor();
    }
    return ChunkingPerformanceMonitor.instance;
  }
  
  /**
   * Inicia el monitoreo de una sesión de chunking
   */
  public startSession(
    sessionId: string,
    originalDocumentSize: number,
    contentType: string,
    config: { maxChunkSize: number; minChunkSize: number; overlapSize: number }
  ): void {
    const metrics: ChunkingPerformanceMetrics = {
      sessionId,
      startTime: Date.now(),
      originalDocumentSize,
      chunksGenerated: 0,
      sectionsDetected: 0,
      averageChunkSize: 0,
      compressionRatio: 0,
      contentType,
      configUsed: config,
      efficiency: {
        chunksPerSecond: 0,
        charactersPerSecond: 0,
        averageTimePerChunk: 0,
        reductionVsNaive: 0,
      },
    };
    
    this.activeSessions.set(sessionId, metrics);
    this.log('info', `Iniciada sesión de chunking: ${sessionId}`, { 
      documentSize: originalDocumentSize,
      contentType,
      config 
    });
  }
  
  /**
   * Actualiza las métricas de una sesión activa
   */
  public updateSession(
    sessionId: string,
    updates: {
      chunksGenerated?: number;
      sectionsDetected?: number;
      averageChunkSize?: number;
    }
  ): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      this.log('warning', `Sesión no encontrada: ${sessionId}`);
      return;
    }
    
    Object.assign(session, updates);
    
    // Calcular ratio de compresión
    if (updates.sectionsDetected && updates.chunksGenerated) {
      session.compressionRatio = updates.chunksGenerated / updates.sectionsDetected;
    }
  }
  
  /**
   * Finaliza una sesión de chunking y calcula métricas finales
   */
  public endSession(sessionId: string): ChunkingPerformanceMetrics | null {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      this.log('warning', `Sesión no encontrada para finalizar: ${sessionId}`);
      return null;
    }
    
    session.endTime = Date.now();
    session.totalDurationMs = session.endTime - session.startTime;
    
    // Calcular métricas de eficiencia
    if (session.totalDurationMs > 0) {
      session.efficiency.chunksPerSecond = (session.chunksGenerated / session.totalDurationMs) * 1000;
      session.efficiency.charactersPerSecond = (session.originalDocumentSize / session.totalDurationMs) * 1000;
      session.efficiency.averageTimePerChunk = session.chunksGenerated > 0 
        ? session.totalDurationMs / session.chunksGenerated 
        : 0;
      
      // Calcular reducción vs chunking naive (estimación)
      const naiveChunks = Math.ceil(session.originalDocumentSize / session.configUsed.maxChunkSize);
      session.efficiency.reductionVsNaive = naiveChunks > 0 
        ? ((naiveChunks - session.chunksGenerated) / naiveChunks) * 100 
        : 0;
    }
    
    // Verificar umbrales y generar alertas
    this.checkPerformanceThresholds(session);
    
    // Mover a histórico
    this.historicalMetrics.push(session);
    this.activeSessions.delete(sessionId);
    
    this.log('info', `Finalizada sesión de chunking: ${sessionId}`, {
      duration: session.totalDurationMs,
      chunks: session.chunksGenerated,
      efficiency: session.efficiency,
    });
    
    return session;
  }
  
  /**
   * Verifica umbrales de rendimiento y genera alertas
   */
  private checkPerformanceThresholds(metrics: ChunkingPerformanceMetrics): void {
    const alerts: PerformanceAlert[] = [];
    
    // Verificar tiempo de procesamiento
    if (metrics.totalDurationMs && metrics.totalDurationMs > this.PERFORMANCE_THRESHOLDS.MAX_PROCESSING_TIME_MS) {
      alerts.push({
        level: 'warning',
        message: `Tiempo de procesamiento excesivo: ${metrics.totalDurationMs}ms`,
        metrics,
        timestamp: Date.now(),
        suggestions: [
          'Considerar aumentar maxChunkSize',
          'Verificar configuración de paralelización',
          'Revisar patrones de sección muy granulares'
        ]
      });
    }
    
    // Verificar número de chunks
    if (metrics.chunksGenerated > this.PERFORMANCE_THRESHOLDS.MAX_CHUNKS_PER_DOCUMENT) {
      alerts.push({
        level: 'critical',
        message: `Demasiados chunks generados: ${metrics.chunksGenerated}`,
        metrics,
        timestamp: Date.now(),
        suggestions: [
          'Aumentar minChunkSize',
          'Revisar patrones de sección',
          'Considerar agrupación más agresiva'
        ]
      });
    }
    
    // Verificar ratio de compresión
    if (metrics.compressionRatio > (1 - this.PERFORMANCE_THRESHOLDS.MIN_COMPRESSION_RATIO)) {
      alerts.push({
        level: 'warning',
        message: `Baja compresión de chunks: ${(metrics.compressionRatio * 100).toFixed(1)}%`,
        metrics,
        timestamp: Date.now(),
        suggestions: [
          'Revisar algoritmo de agrupación inteligente',
          'Ajustar minChunkSize',
          'Verificar patrones de sección'
        ]
      });
    }
    
    // Verificar velocidad de procesamiento
    if (metrics.efficiency.charactersPerSecond < this.PERFORMANCE_THRESHOLDS.MIN_CHARACTERS_PER_SECOND) {
      alerts.push({
        level: 'warning',
        message: `Velocidad de procesamiento baja: ${metrics.efficiency.charactersPerSecond.toFixed(0)} chars/sec`,
        metrics,
        timestamp: Date.now(),
        suggestions: [
          'Optimizar algoritmos de detección de secciones',
          'Considerar procesamiento paralelo',
          'Revisar configuración de timeouts'
        ]
      });
    }
    
    // Enviar alertas
    alerts.forEach(alert => this.sendAlert(alert));
  }
  
  /**
   * Registra una alerta de rendimiento
   */
  private sendAlert(alert: PerformanceAlert): void {
    this.log(alert.level === 'critical' ? 'error' : 'warning', alert.message, alert);
    this.alertCallbacks.forEach(callback => callback(alert));
  }
  
  /**
   * Registra un callback para recibir alertas
   */
  public onAlert(callback: (alert: PerformanceAlert) => void): void {
    this.alertCallbacks.push(callback);
  }
  
  /**
   * Obtiene estadísticas agregadas del rendimiento
   */
  public getAggregatedStats(): {
    totalSessions: number;
    averageProcessingTime: number;
    averageChunksPerDocument: number;
    averageCompressionRatio: number;
    averageEfficiency: number;
  } {
    if (this.historicalMetrics.length === 0) {
      return {
        totalSessions: 0,
        averageProcessingTime: 0,
        averageChunksPerDocument: 0,
        averageCompressionRatio: 0,
        averageEfficiency: 0,
      };
    }
    
    const validMetrics = this.historicalMetrics.filter(m => m.totalDurationMs);
    
    return {
      totalSessions: this.historicalMetrics.length,
      averageProcessingTime: validMetrics.reduce((sum, m) => sum + (m.totalDurationMs || 0), 0) / validMetrics.length,
      averageChunksPerDocument: this.historicalMetrics.reduce((sum, m) => sum + m.chunksGenerated, 0) / this.historicalMetrics.length,
      averageCompressionRatio: this.historicalMetrics.reduce((sum, m) => sum + m.compressionRatio, 0) / this.historicalMetrics.length,
      averageEfficiency: this.historicalMetrics.reduce((sum, m) => sum + m.efficiency.charactersPerSecond, 0) / this.historicalMetrics.length,
    };
  }
  
  /**
   * Limpia el histórico de métricas
   */
  public clearHistory(): void {
    this.historicalMetrics = [];
    this.log('info', 'Histórico de métricas limpiado');
  }
  
  /**
   * Función de logging interna
   */
  private log(level: string, message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [CHUNKING-MONITOR] [${level.toUpperCase()}] ${message}`;
    
    if (process.env.NODE_ENV === 'development') {
      if (data) {
        console.log(logMessage, data);
      } else {
        console.log(logMessage);
      }
    }
  }
}

/**
 * Instancia singleton del monitor de rendimiento
 */
export const chunkingPerformanceMonitor = ChunkingPerformanceMonitor.getInstance();
