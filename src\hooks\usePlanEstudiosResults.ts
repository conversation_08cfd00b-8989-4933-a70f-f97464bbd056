'use client';

import { useEffect, useState } from 'react';
import { useBackgroundTasks } from '@/contexts/BackgroundTasksContext';
import { PlanEstudiosEstructurado } from '@/features/planificacion/services/planGeneratorService';

interface UsePlanEstudiosResultsOptions {
  onResult?: (result: PlanEstudiosEstructurado) => void;
  onError?: (error: string) => void;
}

export const usePlanEstudiosResults = (options: UsePlanEstudiosResultsOptions = {}) => {
  const { tasks, isInitialized } = useBackgroundTasks();
  const [latestResult, setLatestResult] = useState<PlanEstudiosEstructurado | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasCheckedInitialTasks, setHasCheckedInitialTasks] = useState(false);

  // Efecto para verificar tareas iniciales cuando el contexto se inicializa
  useEffect(() => {
    if (!isInitialized || hasCheckedInitialTasks) return;

    const planEstudiosTasks = tasks.filter(task => task.type === 'plan-estudios');

    // Buscar la tarea completada más reciente que podría haberse perdido
    const completedTasks = planEstudiosTasks
      .filter(task => task.status === 'completed' && task.result)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    if (completedTasks.length > 0) {
      const latestTask = completedTasks[0];

      setLatestResult(latestTask.result);

      if (options.onResult) {
        setTimeout(() => {
          options.onResult?.(latestTask.result);
        }, 0);
      }
    }

    // Buscar tareas con error más recientes
    const errorTasks = planEstudiosTasks
      .filter(task => task.status === 'error')
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    if (errorTasks.length > 0 && options.onError) {
      const latestErrorTask = errorTasks[0];

      setTimeout(() => {
        options.onError?.(latestErrorTask.error || 'Error desconocido');
      }, 0);
    }

    setHasCheckedInitialTasks(true);
  }, [isInitialized, hasCheckedInitialTasks, tasks, options]);

  // Efecto para procesar nuevas tareas en tiempo real
  useEffect(() => {
    if (!isInitialized) return;

    const planEstudiosTasks = tasks.filter(task => task.type === 'plan-estudios');

    // Verificar si hay tareas activas
    const hasActiveTasks = planEstudiosTasks.some(task =>
      task.status === 'pending' || task.status === 'processing'
    );
    setIsLoading(hasActiveTasks);

    // Buscar la tarea completada más reciente
    const completedTasks = planEstudiosTasks
      .filter(task => task.status === 'completed' && task.result)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    if (completedTasks.length > 0) {
      const latestTask = completedTasks[0];

      // Solo actualizar si es un resultado diferente
      if (latestTask.result !== latestResult) {
        setLatestResult(latestTask.result);

        if (options.onResult) {
          options.onResult(latestTask.result);
        }
      }
    }

  }, [isInitialized, tasks, options, latestResult]);

  return {
    latestResult,
    isLoading,
    hasResults: !!latestResult
  };
};
