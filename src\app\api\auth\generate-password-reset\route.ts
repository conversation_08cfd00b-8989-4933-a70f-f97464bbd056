// src/app/api/auth/generate-password-reset/route.ts
// Endpoint temporal para generar enlace de recuperación de contraseña

import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json({
        error: 'Email es requerido'
      }, { status: 400 });
    }

    // Generar enlace de recuperación de contraseña
    const { data, error } = await supabaseAdmin.auth.admin.generateLink({
      type: 'recovery',
      email: email,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`
      }
    });

    if (error) {
      console.error('Error generating password reset link:', error);
      return NextResponse.json({
        error: 'Error generando enlace de recuperación'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      link: data.properties?.action_link,
      message: 'Enlace de recuperación generado'
    });

  } catch (error) {
    console.error('Error in generate-password-reset:', error);
    return NextResponse.json({
      error: 'Error interno del servidor'
    }, { status: 500 });
  }
}
