#!/usr/bin/env node

/**
 * Script para probar la validación de tareas duplicadas
 * 
 * Uso:
 * node scripts/test-duplicate-validation.js
 */

// Cargar variables de entorno desde .env.local
require('dotenv').config({ path: '.env.local' });

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001';

async function testDuplicateValidation() {
  console.log('🧪 Probando validación de tareas duplicadas...\n');

  try {
    // Simular datos de una petición de test
    const requestBody = {
      action: 'generarTest',
      peticion: 'Test de validación de duplicados',
      contextos: ['Contenido de prueba para generar un test'],
      cantidad: 5
    };

    console.log('1️⃣ Enviando primera petición...');
    
    // Primera petición - debería funcionar
    const response1 = await fetch(`${APP_URL}/api/ai`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Nota: En un test real necesitarías autenticación válida
      },
      body: JSON.stringify(requestBody)
    });

    console.log(`   Status: ${response1.status}`);
    
    if (response1.ok) {
      const data1 = await response1.json();
      console.log(`   ✅ Primera petición exitosa: ${JSON.stringify(data1)}`);
    } else {
      const error1 = await response1.text();
      console.log(`   ❌ Primera petición falló: ${error1}`);
    }

    console.log('\n2️⃣ Enviando segunda petición inmediatamente...');
    
    // Segunda petición inmediata - debería fallar con 409
    const response2 = await fetch(`${APP_URL}/api/ai`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    console.log(`   Status: ${response2.status}`);
    
    if (response2.status === 409) {
      const error2 = await response2.json();
      console.log(`   ✅ Segunda petición correctamente rechazada: ${error2.error}`);
    } else if (response2.ok) {
      const data2 = await response2.json();
      console.log(`   ⚠️ Segunda petición inesperadamente exitosa: ${JSON.stringify(data2)}`);
    } else {
      const error2 = await response2.text();
      console.log(`   ❌ Segunda petición falló con error inesperado: ${error2}`);
    }

    console.log('\n🎉 Test de validación completado');

  } catch (error) {
    console.error('\n❌ Error en el test:', error.message);
  }
}

// Ejecutar test
testDuplicateValidation();
