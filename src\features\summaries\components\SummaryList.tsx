'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Eye, FiTrash2 } from 'react-icons/fi';
import { Resumen, obtenerResumenes, eliminarResumen } from '@/lib/supabase/resumenesService';

import { markdownToHTML } from '@/utils/markdownToHTML';

interface SummaryListProps {
  refreshTrigger?: number;
}

export default function SummaryList({ refreshTrigger }: SummaryListProps) {
  const [resumenes, setResumenes] = useState<Resumen[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [resumenSeleccionado, setResumenSeleccionado] = useState<Resumen | null>(null);
  const [mostrarModal, setMostrarModal] = useState(false);



  const cargarResumenes = async () => {
    try {
      setIsLoading(true);
      const data = await obtenerResumenes();
      setResumenes(data);
    } catch (error) {
      console.error('Error al cargar resúmenes:', error);
      toast.error('Error al cargar los resúmenes');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    cargarResumenes();
  }, [refreshTrigger]);

  const handleEliminar = async (id: string, titulo: string) => {
    if (!confirm(`¿Estás seguro de que quieres eliminar el resumen "${titulo}"?`)) {
      return;
    }

    try {
      const success = await eliminarResumen(id);
      if (success) {
        toast.success('Resumen eliminado exitosamente');
        await cargarResumenes(); // Recargar la lista
      } else {
        toast.error('Error al eliminar el resumen');
      }
    } catch (error) {
      console.error('Error al eliminar resumen:', error);
      toast.error('Error al eliminar el resumen');
    }
  };

  const handleVerResumen = (resumen: Resumen) => {
    setResumenSeleccionado(resumen);
    setMostrarModal(true);
  };





  const handleImprimir = (resumen: Resumen) => {
    try {
      const htmlContent = markdownToHTML(
        resumen.contenido,
        resumen.titulo,
        {
          createdAt: resumen.creado_en,
          instructions: resumen.instrucciones || undefined,
          author: 'OposiAI'
        }
      );

      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${resumen.titulo}</title>
              <style>
                * { box-sizing: border-box; }
                body {
                  font-family: Arial, sans-serif;
                  margin: 20mm;
                  line-height: 1.6;
                  color: #333;
                  font-size: 12px;
                }
                h1 {
                  color: #2563eb;
                  border-bottom: 2px solid #2563eb;
                  padding-bottom: 10px;
                  margin: 0 0 20px 0;
                  font-size: 24px;
                  text-align: left;
                }
                h2 {
                  color: #1e40af;
                  border-bottom: 1px solid #cbd5e1;
                  padding-bottom: 5px;
                  margin: 25px 0 15px 0;
                  font-size: 20px;
                  text-align: left;
                }
                h3 {
                  color: #1e40af;
                  margin: 20px 0 10px 0;
                  font-size: 16px;
                  text-align: left;
                }
                p {
                  margin: 12px 0;
                  text-align: justify;
                  text-justify: inter-word;
                  hyphens: auto;
                  word-wrap: break-word;
                }
                strong {
                  color: #1e40af;
                  font-weight: bold;
                }
                em {
                  font-style: italic;
                  color: #64748b;
                }
                ul, ol {
                  margin: 12px 0;
                  padding-left: 20px;
                }
                li {
                  margin: 6px 0;
                  text-align: justify;
                }
                blockquote {
                  margin: 15px 0;
                  padding: 12px 15px;
                  background-color: #f8fafc;
                  border-left: 4px solid #2563eb;
                  font-style: italic;
                  text-align: justify;
                }
                .metadata {
                  font-size: 11px;
                  color: #64748b;
                  margin-bottom: 25px;
                  padding: 12px;
                  background-color: #f8fafc;
                  border-radius: 5px;
                  border: 1px solid #e2e8f0;
                }
                .metadata p {
                  margin: 4px 0;
                  text-align: left;
                }
                @media print {
                  body {
                    margin: 20mm;
                    font-size: 12px;
                  }
                  .metadata {
                    background-color: #f9f9f9;
                    border: 1px solid #ddd;
                  }
                  blockquote {
                    background-color: #f9f9f9;
                  }
                }
              </style>
            </head>
            <body>
              ${htmlContent}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
      }
    } catch (error) {
      console.error('Error al imprimir:', error);
      toast.error('Error al preparar la impresión');
    }
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Cargando resúmenes...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-800">📚 Mis Resúmenes</h3>
        <button
          onClick={cargarResumenes}
          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
          </svg>
          Actualizar
        </button>
      </div>



      {resumenes.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="text-gray-600 mb-2">No tienes resúmenes creados</p>
          <p className="text-sm text-gray-500">
            Selecciona un documento y genera tu primer resumen para empezar a estudiar de manera más eficiente.
          </p>
        </div>
      ) : (
        <div className="grid gap-4">
          {resumenes.map((resumen) => (
            <div key={resumen.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-gray-900">{resumen.titulo}</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Creado: {formatearFecha(resumen.creado_en)}
                  </p>
                  {resumen.instrucciones && (
                    <p className="text-xs text-gray-500 mb-2 italic">
                      "{resumen.instrucciones.substring(0, 100)}{resumen.instrucciones.length > 100 ? '...' : ''}"
                    </p>
                  )}
                  <p className="text-xs text-gray-400">
                    Contenido: ~{resumen.contenido.split(/\s+/).length} palabras
                  </p>
                </div>
                <div className="flex flex-wrap gap-2 ml-4">
                  <button
                    onClick={() => handleVerResumen(resumen)}
                    className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors"
                    title="Ver resumen completo"
                  >
                    <FiEye className="w-3 h-3" />
                    Ver
                  </button>



                  <button
                    onClick={() => handleImprimir(resumen)}
                    className="flex items-center gap-1 bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors"
                    title="Imprimir resumen"
                  >
                    <FiPrinter className="w-3 h-3" />
                    Imprimir
                  </button>
                  <button
                    onClick={() => handleEliminar(resumen.id, resumen.titulo)}
                    className="flex items-center gap-1 bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors"
                    title="Eliminar resumen"
                  >
                    <FiTrash2 className="w-3 h-3" />
                    Eliminar
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal para ver resumen completo */}
      {mostrarModal && resumenSeleccionado && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setMostrarModal(false)}
          />
          {/* Modal */}
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-11/12 max-w-4xl max-h-5/6 bg-white rounded-lg shadow-xl z-50 overflow-hidden">
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">{resumenSeleccionado.titulo}</h3>
              <button
                onClick={() => setMostrarModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>


            <div className="p-4 overflow-y-auto max-h-96">
              <div className="mb-4 text-sm text-gray-600">
                <p><strong>Creado:</strong> {formatearFecha(resumenSeleccionado.creado_en)}</p>
                {resumenSeleccionado.instrucciones && (
                  <p><strong>Instrucciones:</strong> {resumenSeleccionado.instrucciones}</p>
                )}
              </div>
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{
                  __html: resumenSeleccionado.contenido
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                    .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
                    .replace(/^\- (.*$)/gim, '<li>$1</li>')
                    .replace(/\n/g, '<br />')
                }}
              />
            </div>
            <div className="p-4 border-t border-gray-200 flex justify-between items-center">
              <div className="flex gap-2">

                <button
                  onClick={() => handleImprimir(resumenSeleccionado)}
                  className="flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded focus:outline-none focus:shadow-outline transition-colors"
                >
                  <FiPrinter className="w-4 h-4" />
                  Imprimir
                </button>
              </div>
              <button
                onClick={() => setMostrarModal(false)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded focus:outline-none focus:shadow-outline transition-colors"
              >
                Cerrar
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
