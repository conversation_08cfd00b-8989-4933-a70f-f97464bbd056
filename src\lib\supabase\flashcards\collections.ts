/**
 * Servicio para gestión de colecciones de flashcards
 * 
 * Responsabilidades:
 * - CRUD de colecciones de flashcards
 * - Validación de permisos de usuario
 * - Cálculo de estadísticas básicas
 */

import {
  supabase,
  ColeccionFlashcards
} from '../supabaseClient';
import { obtenerUsuarioActual } from '../authService';

/**
 * Crea una nueva colección de flashcards
 */
export async function crearColeccionFlashcards(titulo: string, descripcion?: string): Promise<string | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .insert([{
        titulo,
        descripcion,
        user_id: user.id
      }])
      .select();

    if (error) {
      console.error('Error al crear colección de flashcards:', error);
      return null;
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error al crear colección de flashcards:', error);
    return null;
  }
}

/**
 * Obtiene todas las colecciones de flashcards del usuario actual
 */
export async function obtenerColeccionesFlashcards(): Promise<ColeccionFlashcards[]> {
  try {
    // Obtener el usuario actual
    const { user, error: userError } = await obtenerUsuarioActual();

    if (userError) {
      console.error('Error al obtener usuario:', userError);
      return [];
    }

    if (!user) {
      console.error('No hay usuario autenticado');
      return [];
    }

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .select('*')
      .eq('user_id', user.id)
      .order('creado_en', { ascending: false });

    if (error) {
      console.error('Error al obtener colecciones de flashcards:', error);
      return [];
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Obtener el conteo de flashcards y estadísticas básicas para cada colección
    const coleccionesConConteo = await Promise.all(
      data.map(async (coleccion) => {
        try {
          // Usar una consulta simple para contar flashcards
          const { data: flashcardsData, error: flashcardsError } = await supabase
            .from('flashcards')
            .select('id')
            .eq('coleccion_id', coleccion.id);

          if (flashcardsError) {
            console.error('Error al contar flashcards para colección', coleccion.id, ':', flashcardsError);
            return {
              ...coleccion,
              numero_flashcards: 0,
              pendientes_hoy: 0,
            };
          }

          // Obtener flashcards pendientes para hoy
          const { data: pendientesData, error: pendientesError } = await supabase
            .from('flashcards')
            .select(`
              id,
              progreso_flashcards!inner(
                proxima_revision,
                estado
              )
            `)
            .eq('coleccion_id', coleccion.id)
            .lte('progreso_flashcards.proxima_revision', new Date().toISOString());

          const pendientesHoy = pendientesError ? 0 : (pendientesData?.length || 0);

          return {
            ...coleccion,
            numero_flashcards: flashcardsData?.length || 0,
            pendientes_hoy: pendientesHoy,
          };
        } catch (error) {
          console.error('Error al procesar colección', coleccion.id, ':', error);
          return {
            ...coleccion,
            numero_flashcards: 0,
            pendientes_hoy: 0,
          };
        }
      })
    );

    return coleccionesConConteo;
  } catch (error) {
    console.error('Error general al obtener colecciones de flashcards:', error);
    return [];
  }
}

/**
 * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)
 */
export async function obtenerColeccionFlashcardsPorId(id: string): Promise<ColeccionFlashcards | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error al obtener colección de flashcards:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener colección de flashcards:', error);
    return null;
  }
}

/**
 * Actualiza una colección de flashcards existente
 */
export async function actualizarColeccionFlashcards(
  coleccionId: string,
  titulo: string,
  descripcion?: string
): Promise<ColeccionFlashcards | null> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .update({
        titulo,
        descripcion: descripcion || null,
        actualizado_en: new Date().toISOString()
      })
      .eq('id', coleccionId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) {
      console.error('Error al actualizar colección:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al actualizar colección:', error);
    return null;
  }
}

/**
 * Elimina una colección completa de flashcards y todo su contenido asociado
 */
export async function eliminarColeccionFlashcards(coleccionId: string): Promise<boolean> {
  try {
    // Obtener el usuario actual para verificar permisos
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      return false;
    }

    // Primero obtener todas las flashcards de la colección
    const { data: flashcards, error: errorFlashcards } = await supabase
      .from('flashcards')
      .select('id')
      .eq('coleccion_id', coleccionId);

    if (errorFlashcards) {
      return false;
    }

    const flashcardIds = flashcards?.map(fc => fc.id) || [];

    // Eliminar progreso de todas las flashcards
    if (flashcardIds.length > 0) {
      const { error: errorProgreso } = await supabase
        .from('progreso_flashcards')
        .delete()
        .in('flashcard_id', flashcardIds);

      if (errorProgreso) {
        return false;
      }

      // Eliminar historial de todas las flashcards
      const { error: errorHistorial } = await supabase
        .from('historial_revisiones')
        .delete()
        .in('flashcard_id', flashcardIds);

      if (errorHistorial) {
        return false;
      }

      // Eliminar todas las flashcards de la colección
      const { error: errorFlashcardsDelete } = await supabase
        .from('flashcards')
        .delete()
        .eq('coleccion_id', coleccionId);

      if (errorFlashcardsDelete) {
        return false;
      }
    }

    // Finalmente eliminar la colección
    const { error: errorColeccion, count } = await supabase
      .from('colecciones_flashcards')
      .delete({ count: 'exact' })
      .eq('id', coleccionId)
      .eq('user_id', user.id); // Asegurar que solo se eliminen colecciones del usuario actual

    if (errorColeccion) {
      return false;
    }

    if (count === 0) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}
