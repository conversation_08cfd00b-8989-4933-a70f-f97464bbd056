// src/lib/supabase/storageService.server.ts
// Funciones de Storage específicas para el servidor
import { createServerSupabaseClient } from './server';
import { supabaseAdmin } from './admin';

const BUCKET_NAME = 'documentos_usuario';

/**
 * Sube un archivo desde el servidor usando el contexto de autenticación del servidor
 */
export async function uploadFileFromServer(path: string, file: ArrayBuffer | File, contentType: string): Promise<string | null> {
  try {
    console.log('📤 [STORAGE_SERVER] Iniciando subida de archivo desde servidor:', {
      path,
      contentType,
      size: file instanceof File ? file.size : file.byteLength,
      timestamp: new Date().toISOString()
    });

    // Usar cliente de servidor con contexto de autenticación
    const serverSupabase = await createServerSupabaseClient();
    
    // Verificar autenticación del servidor
    const { data: { user }, error: authError } = await serverSupabase.auth.getUser();
    console.log('🔐 [STORAGE_SERVER] Contexto de autenticación del servidor:', {
      hasUser: !!user,
      userId: user?.id || 'null',
      authError: authError?.message || 'none',
      timestamp: new Date().toISOString()
    });

    // Determinar qué cliente usar
    let clientToUse = serverSupabase;
    let useAdminClient = false;

    // Si no hay usuario autenticado, usar cliente admin
    if (!user || authError) {
      console.log('🔧 [STORAGE_SERVER] Usando cliente de administrador debido a falta de autenticación');
      console.log('🔧 [STORAGE_SERVER] Detalles del error de autenticación:', {
        hasUser: !!user,
        authError: authError?.message,
        authErrorCode: authError?.status
      });
      clientToUse = supabaseAdmin;
      useAdminClient = true;
    }

    const { data, error } = await clientToUse.storage
      .from(BUCKET_NAME)
      .upload(path, file, {
        contentType,
        upsert: true,
      });

    if (error) {
      console.error('❌ [STORAGE_SERVER] Error al subir archivo:', {
        path,
        error: error.message,
        errorDetails: error,
        usedAdminClient: useAdminClient,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    console.log('✅ [STORAGE_SERVER] Archivo subido exitosamente:', {
      path: data.path,
      usedAdminClient: useAdminClient,
      timestamp: new Date().toISOString()
    });

    return data.path;
  } catch (error) {
    console.error('❌ [STORAGE_SERVER] Error inesperado al subir archivo:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return null;
  }
}

/**
 * Descarga un archivo desde el servidor
 */
export async function downloadFileFromServer(path: string): Promise<ArrayBuffer | null> {
  try {
    console.log('📥 [STORAGE_SERVER] Iniciando descarga de archivo desde servidor:', {
      path,
      timestamp: new Date().toISOString()
    });

    const serverSupabase = await createServerSupabaseClient();
    const { data, error } = await serverSupabase.storage
      .from(BUCKET_NAME)
      .download(path);

    if (error) {
      console.error('❌ [STORAGE_SERVER] Error al descargar archivo:', {
        path,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    console.log('✅ [STORAGE_SERVER] Archivo descargado exitosamente:', {
      path,
      size: data.size,
      timestamp: new Date().toISOString()
    });

    return await data.arrayBuffer();
  } catch (error) {
    console.error('❌ [STORAGE_SERVER] Error inesperado al descargar archivo:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return null;
  }
}

/**
 * Descarga el contenido de un archivo como texto desde el servidor
 * Esta función está optimizada para uso en API routes del servidor
 * Extrae automáticamente el texto de archivos PDF
 */
export async function downloadFileContentFromServer(path: string): Promise<string | null> {
  try {
    console.log('📥 [STORAGE_SERVER] Iniciando descarga de contenido desde servidor:', {
      path,
      timestamp: new Date().toISOString()
    });

    const serverSupabase = await createServerSupabaseClient();
    const { data: { user }, error: authError } = await serverSupabase.auth.getUser();

    if (authError || !user) {
      console.error('❌ [STORAGE_SERVER] Error de autenticación al descargar archivo:', {
        path,
        authError: authError?.message,
        hasUser: !!user,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    console.log('🔐 [STORAGE_SERVER] Usuario autenticado para descarga:', {
      userId: user.id,
      path,
      timestamp: new Date().toISOString()
    });

    const { data, error } = await serverSupabase.storage
      .from(BUCKET_NAME)
      .download(path);

    if (error) {
      console.error('❌ [STORAGE_SERVER] Error al descargar archivo:', {
        path,
        error: JSON.stringify(error, null, 2),
        errorMessage: error.message,
        userId: user.id,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    if (!data) {
      console.error('❌ [STORAGE_SERVER] No se recibieron datos del archivo:', {
        path,
        userId: user.id,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    // Determinar el tipo de archivo y extraer contenido apropiadamente
    const fileExtension = path.toLowerCase().split('.').pop();
    let content: string;

    if (fileExtension === 'pdf') {
      console.log('📄 [STORAGE_SERVER] Detectado archivo PDF, extrayendo texto...');
      content = await extractTextFromPDF(data);
    } else {
      // Para archivos de texto plano
      content = await data.text();
    }

    console.log('✅ [STORAGE_SERVER] Contenido procesado exitosamente:', {
      path,
      fileType: fileExtension,
      contentLength: content.length,
      userId: user.id,
      timestamp: new Date().toISOString()
    });

    return content;
  } catch (error) {
    console.error('❌ [STORAGE_SERVER] Error inesperado al descargar contenido:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      errorStack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
    return null;
  }
}

/**
 * Extrae texto de un archivo PDF usando pdf-parse
 */
async function extractTextFromPDF(pdfData: Blob): Promise<string> {
  try {
    // Importar pdf-parse dinámicamente
    const pdfParse = (await import('pdf-parse')).default;

    // Convertir Blob a Buffer
    const arrayBuffer = await pdfData.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    console.log('🔍 [PDF_PARSER] Iniciando extracción de texto del PDF...');

    // Extraer texto del PDF
    const pdfData_parsed = await pdfParse(buffer);

    console.log('✅ [PDF_PARSER] Texto extraído exitosamente:', {
      pages: pdfData_parsed.numpages,
      textLength: pdfData_parsed.text.length,
      timestamp: new Date().toISOString()
    });

    // Post-procesar el texto para restaurar estructura de secciones
    const processedText = postProcessPDFText(pdfData_parsed.text);

    console.log('🔧 [PDF_PARSER] Texto post-procesado:', {
      originalLength: pdfData_parsed.text.length,
      processedLength: processedText.length,
      timestamp: new Date().toISOString()
    });

    return processedText;
  } catch (error) {
    console.error('❌ [PDF_PARSER] Error al extraer texto del PDF:', {
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    throw new Error(`No se pudo extraer el texto del PDF: ${error instanceof Error ? error.message : 'Error desconocido'}`);
  }
}

/**
 * Post-procesa el texto extraído del PDF para restaurar la estructura de secciones
 * que el sistema de chunking pueda reconocer correctamente
 */
function postProcessPDFText(rawText: string): string {
  console.log('🔧 [PDF_POSTPROCESS] Iniciando post-procesamiento del texto...');

  let processedText = rawText;

  // 1. Limpiar caracteres extraños y normalizar espacios
  processedText = processedText
    .replace(/\r\n/g, '\n')  // Normalizar saltos de línea
    .replace(/\r/g, '\n')    // Convertir \r a \n
    .replace(/\n{3,}/g, '\n\n') // Reducir múltiples saltos de línea
    .replace(/[ \t]+/g, ' ')  // Normalizar espacios
    .trim();

  // 2. Detectar y marcar títulos de secciones principales
  // Patrones comunes en documentos académicos/técnicos
  const sectionPatterns = [
    // Números con punto y guión: "1.- Introducción"
    /^(\d+)\.\s*-\s*(.+)$/gm,
    // Números con punto: "1. Introducción"
    /^(\d+)\.\s+([A-ZÁÉÍÓÚÑÜ][^.\n]{10,})$/gm,
    // Apartados: "APARTADO 1: Título"
    /^(APARTADO\s+\d+)[:\.-]\s*(.+)$/gim,
    // Temas: "TEMA 1: Título"
    /^(TEMA\s+\d+)[:\.-]\s*(.+)$/gim,
    // Capítulos: "CAPÍTULO 1: Título"
    /^(CAPÍTULO\s+\d+)[:\.-]\s*(.+)$/gim,
    // Secciones: "SECCIÓN 1: Título"
    /^(SECCIÓN\s+\d+)[:\.-]\s*(.+)$/gim
  ];

  // Aplicar marcadores de sección
  sectionPatterns.forEach((pattern, index) => {
    processedText = processedText.replace(pattern, (match, num, title) => {
      const cleanTitle = title.trim();
      return `\n\n${num}.- ${cleanTitle}\n\n`;
    });
  });

  // 3. Detectar y marcar subsecciones
  const subsectionPatterns = [
    // Subsecciones: "1.1.- Subtítulo"
    /^(\d+\.\d+)\.\s*-\s*(.+)$/gm,
    // Subsecciones: "1.1. Subtítulo"
    /^(\d+\.\d+)\.\s+([A-ZÁÉÍÓÚÑÜ][^.\n]{8,})$/gm,
    // Sub-subsecciones: "1.1.1.- Subtítulo"
    /^(\d+\.\d+\.\d+)\.\s*-\s*(.+)$/gm
  ];

  subsectionPatterns.forEach((pattern) => {
    processedText = processedText.replace(pattern, (match, num, title) => {
      const cleanTitle = title.trim();
      return `\n${num}.- ${cleanTitle}\n`;
    });
  });

  // 4. Detectar listas y elementos estructurados
  // Elementos de lista con letras: "a) Elemento"
  processedText = processedText.replace(/^([a-z])\)\s*(.+)$/gm, '\n$1) $2');

  // Elementos de lista con números romanos: "I.- Elemento"
  processedText = processedText.replace(/^([IVX]+)\.\s*-\s*(.+)$/gm, '\n$1.- $2');

  // 5. Limpiar espacios finales y normalizar
  processedText = processedText
    .replace(/[ \t]+$/gm, '') // Quitar espacios al final de líneas
    .replace(/\n{3,}/g, '\n\n') // Normalizar saltos de línea múltiples
    .trim();

  console.log('✅ [PDF_POSTPROCESS] Post-procesamiento completado');

  return processedText;
}

/**
 * Elimina un archivo desde el servidor
 */
export async function deleteFileFromServer(path: string): Promise<boolean> {
  try {
    console.log('🗑️ [STORAGE_SERVER] Iniciando eliminación de archivo desde servidor:', {
      path,
      timestamp: new Date().toISOString()
    });

    const serverSupabase = await createServerSupabaseClient();
    const { error } = await serverSupabase.storage
      .from(BUCKET_NAME)
      .remove([path]);

    if (error) {
      console.error('❌ [STORAGE_SERVER] Error al eliminar archivo:', {
        path,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return false;
    }

    console.log('✅ [STORAGE_SERVER] Archivo eliminado exitosamente:', {
      path,
      timestamp: new Date().toISOString()
    });

    return true;
  } catch (error) {
    console.error('❌ [STORAGE_SERVER] Error inesperado al eliminar archivo:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return false;
  }
}
