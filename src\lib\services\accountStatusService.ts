// src/lib/services/accountStatusService.ts
// Servicio centralizado para determinar el estado de la cuenta de usuario

import { supabase } from '@/lib/supabase/client';
import { obtenerUsuarioActual } from '@/lib/supabase/authService';

export type AccountStatus = 'active_paid' | 'active_free' | 'degraded_from_paid' | 'unauthenticated';

export interface AccountStatusResult {
  status: AccountStatus;
  plan: 'free' | 'usuario' | 'pro' | 'none';
  userId: string | null;
  isDegraded: boolean;
  isAuthenticated: boolean;
}

/**
 * Obtiene el estado actual de la cuenta del usuario
 * Esta función centraliza toda la lógica para determinar si un usuario
 * tiene acceso completo, acceso limitado, o está bloqueado
 */
export async function getAccountStatus(): Promise<AccountStatusResult> {
  try {
    // 1. Verificar autenticación
    const { user } = await obtenerUsuarioActual();
    if (!user) {
      return {
        status: 'unauthenticated',
        plan: 'none',
        userId: null,
        isDegraded: false,
        isAuthenticated: false
      };
    }

    // 2. Obtener perfil del usuario
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('subscription_plan, is_degraded, payment_verified')
      .eq('user_id', user.id)
      .single();

    if (error || !profile) {
      console.error('Error obteniendo perfil de usuario:', error);
      // Por seguridad, tratar como degradado hasta que se suscriba
      return {
        status: 'degraded_from_paid',
        plan: 'free',
        userId: user.id,
        isDegraded: true,
        isAuthenticated: true
      };
    }

    // 3. Determinar estado basado en el perfil
    const { subscription_plan, is_degraded, payment_verified } = profile;

    // Si no es plan gratuito, es usuario activo de pago
    if (subscription_plan !== 'free') {
      return {
        status: 'active_paid',
        plan: subscription_plan,
        userId: user.id,
        isDegraded: false,
        isAuthenticated: true
      };
    }

    // Es plan 'free', ahora diferenciamos entre nuevo usuario y degradado
    if (is_degraded) {
      return {
        status: 'degraded_from_paid',
        plan: 'free',
        userId: user.id,
        isDegraded: true,
        isAuthenticated: true
      };
    } else {
      return {
        status: 'active_free',
        plan: 'free',
        userId: user.id,
        isDegraded: false,
        isAuthenticated: true
      };
    }

  } catch (err) {
    console.error("Error en getAccountStatus:", err);
    // En caso de error, devolver estado no autenticado por seguridad
    return {
      status: 'unauthenticated',
      plan: 'none',
      userId: null,
      isDegraded: false,
      isAuthenticated: false
    };
  }
}

/**
 * Verifica si el usuario tiene acceso a funcionalidades
 */
export async function hasFeatureAccess(): Promise<boolean> {
  const status = await getAccountStatus();
  return status.status !== 'degraded_from_paid' && status.status !== 'unauthenticated';
}

/**
 * Verifica si el usuario necesita ser redirigido a la página de upgrade
 */
export async function needsUpgrade(): Promise<boolean> {
  const status = await getAccountStatus();
  return status.status === 'degraded_from_paid';
}
