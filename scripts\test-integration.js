#!/usr/bin/env node
// scripts/test-integration.js
// Script para ejecutar tests de integración con Supabase

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Iniciando tests de integración con Supabase...\n');

// Verificar que estamos en el directorio correcto
const projectRoot = path.resolve(__dirname, '..');
process.chdir(projectRoot);

// Verificar variables de entorno
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Variables de entorno faltantes:');
  missingVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  console.error('\nPor favor, configura estas variables en tu archivo .env.local');
  process.exit(1);
}

console.log('✅ Variables de entorno verificadas');

try {
  // Ejecutar tests de integración específicos
  console.log('\n📋 Ejecutando tests de integración...\n');
  
  const testCommand = [
    'npm test',
    'src/__tests__/integration/supabaseDegradationFlow.test.ts',
    '--',
    '--verbose',
    '--setupFilesAfterEnv=src/__tests__/setup/integrationSetup.ts',
    '--testTimeout=30000'
  ].join(' ');

  execSync(testCommand, { 
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'test' }
  });

  console.log('\n🎉 Tests de integración completados exitosamente!');

} catch (error) {
  console.error('\n❌ Error ejecutando tests de integración:');
  console.error(error.message);
  
  if (error.message.includes('connection')) {
    console.error('\n💡 Posibles soluciones:');
    console.error('   1. Verificar conexión a internet');
    console.error('   2. Verificar que las credenciales de Supabase sean correctas');
    console.error('   3. Verificar que el proyecto de Supabase esté activo');
  }
  
  process.exit(1);
}
