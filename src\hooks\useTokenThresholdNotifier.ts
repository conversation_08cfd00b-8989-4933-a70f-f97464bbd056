'use client';

import { useEffect, useState, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { usePlanLimits } from './usePlanLimits';

/**
 * Hook dedicado a observar el uso de tokens y disparar notificaciones
 * cuando se cruzan umbrales predefinidos.
 */
export function useTokenThresholdNotifier() {
  const { userPlan, tokenUsage, loading } = usePlanLimits();
  const [shownThresholds, setShownThresholds] = useState<Set<number>>(new Set());
  
  // Usamos una ref para asegurarnos de que solo se notifique tras la carga inicial
  // y ante cambios reales, no en el primer render.
  const isInitialMount = useRef(true);

  useEffect(() => {
    // No hacer nada mientras se carga la información inicial del plan
    if (loading) {
      return;
    }

    // Si es el primer render después de la carga, marcamos como montado y salimos.
    // Esto evita mostrar notificaciones antiguas al cargar la página.
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Validaciones para asegurar que debemos notificar
    if (!userPlan || (userPlan !== 'usuario' && userPlan !== 'pro') || !tokenUsage) {
      return;
    }

    const percentage = tokenUsage.percentage || 0;
    const thresholds = [100, 90, 75, 50];

    for (const threshold of thresholds) {
      // Comprobar si hemos alcanzado un nuevo umbral que no se ha notificado antes
      if (percentage >= threshold && !shownThresholds.has(threshold)) {
        const notificationType = threshold >= 90 ? 'error' : 'warning';
        const title = `Límite de Tokens: ${threshold}% alcanzado`;
        const message = `Has consumido el ${percentage.toFixed(0)}% de tus tokens. Considera revisar tu uso.`;

        // Usar la función correspondiente de react-hot-toast
        if (notificationType === 'error') {
          toast.error(`${title}\n${message}`, {
            duration: 10000, // Notificación más duradera: 10 segundos
            id: `token-threshold-${threshold}` // ID único para evitar duplicados del mismo toast
          });
        } else {
          // Para warnings, usar toast con estilo personalizado
          toast(`${title}\n${message}`, {
            duration: 10000, // Notificación más duradera: 10 segundos
            id: `token-threshold-${threshold}`, // ID único para evitar duplicados del mismo toast
            style: {
              background: '#f59e0b',
              color: '#fff',
            },
            icon: '⚠️'
          });
        }

        // Registrar que este umbral ya fue notificado en esta sesión
        setShownThresholds(prev => new Set(prev).add(threshold));
        
        // Romper el bucle para mostrar solo la notificación del umbral más alto alcanzado en este cambio
        break; 
      }
    }
  }, [userPlan, tokenUsage, loading, shownThresholds]); // Dependencias del efecto
}
