/**
 * Servicio de Flashcards - Punto de entrada principal
 *
 * Este archivo mantiene la compatibilidad con el código existente
 * mientras delega toda la funcionalidad a los módulos especializados.
 *
 * ARQUITECTURA MODULAR:
 * - collections.ts: Gestión de colecciones
 * - cards.ts: Gestión de flashcards individuales
 * - progress.ts: Gestión del progreso
 * - spaced-repetition.ts: Algoritmo SM-2
 * - study-modes.ts: Modos de estudio por colección
 * - global-study.ts: Modos de estudio globales
 * - index.ts: Punto de entrada del módulo
 */

// Re-exportar todas las funciones desde el módulo modular
export * from './flashcards';
