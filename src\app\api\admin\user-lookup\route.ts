import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { supabaseAdmin } from '@/lib/supabase/admin';

const ADMIN_EMAILS = ['<EMAIL>'];

export async function GET(request: NextRequest) {
  // 1. Verificación de administrador
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    { cookies: { getAll: () => request.cookies.getAll() } }
  );
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user || !ADMIN_EMAILS.includes(user.email!)) {
    return NextResponse.json({ error: 'Acceso denegado' }, { status: 403 });
  }

  // 2. Obtener la query de búsqueda
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query');
  if (!query) {
    return NextResponse.json({ error: 'Query de búsqueda requerida' }, { status: 400 });
  }

  // 3. Buscar usuario por email o ID
  let userData = null;
  let profileData = null;

  if (query.includes('@')) { // Asumimos que es un email
    // Buscar usuario por email usando listUsers sin filtros y luego filtrar
    const { data: allUsers, error: userError } = await supabaseAdmin.auth.admin.listUsers();
    if (!userError && allUsers?.users) {
      userData = allUsers.users.find(user => user.email === query);
    }
  } else { // Asumimos que es un ID
    const { data: foundUser, error: userError } = await supabaseAdmin.auth.admin.getUserById(query);
    if (!userError) userData = foundUser.user;
  }

  if (!userData) {
    return NextResponse.json({ error: 'Usuario no encontrado' }, { status: 404 });
  }

  // 4. Buscar perfil del usuario
  const { data: foundProfile, error: profileError } = await supabaseAdmin
    .from('user_profiles')
    .select('*')
    .eq('user_id', userData.id)
    .single();
  if (!profileError) profileData = foundProfile;

  return NextResponse.json({ success: true, auth: userData, profile: profileData });
}
