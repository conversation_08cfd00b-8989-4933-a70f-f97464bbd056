// src/lib/services/tokenEstimationService.ts
// Servicio para estimación dinámica de tokens basada en contenido real

import { estimateTokens } from '@/lib/ai/tokenTracker';
import { type Chunk } from '@/types/chunking';

/**
 * Configuración de tokens base por tipo de generación
 * Estos valores representan el coste mínimo por item generado
 */
const BASE_TOKENS_PER_ITEM = {
  TEST_GENERATION: 500,        // ~500 tokens por pregunta de test
  FLASHCARD_GENERATION: 300,   // ~300 tokens por flashcard
  MIND_MAP_GENERATION: 4000,   // ~4000 tokens por mapa mental (no se multiplica por cantidad)
  SUMMARY_GENERATION: 6000,    // ~6000 tokens por resumen (no se multiplica por cantidad)
  AI_CHAT: 2000,              // ~2000 tokens por respuesta de chat
  STUDY_PLANNING: 20000       // ~20000 tokens para plan de estudios (entrada controlada)
} as const;

/**
 * Margen de seguridad por defecto (30%)
 * Puede ajustarse según datos reales de consumo
 */
const DEFAULT_SAFETY_MARGIN = 0.3;

/**
 * Límites máximos por solicitud para evitar estimaciones excesivas
 */
const MAX_TOKENS_PER_REQUEST = {
  TEST_GENERATION: 50000,      // Máximo para tests
  FLASHCARD_GENERATION: 30000, // Máximo para flashcards
  MIND_MAP_GENERATION: 25000,  // Máximo para mapas mentales
  SUMMARY_GENERATION: 40000,   // Máximo para resúmenes
  AI_CHAT: 20000,             // Máximo para chat
  STUDY_PLANNING: 25000       // Máximo para planificación (aunque usa estimación fija)
} as const;

export type EstimationType = keyof typeof BASE_TOKENS_PER_ITEM;

/**
 * Resultado de estimación de tokens
 */
export interface TokenEstimation {
  contextTokens: number;
  generationTokens: number;
  safetyMargin: number;
  totalEstimated: number;
  isWithinLimits: boolean;
  maxAllowed: number;
  chunkingOverhead?: number; // Overhead adicional por procesamiento de chunks
  chunksCount?: number; // Número de chunks procesados
}

/**
 * Servicio para estimación dinámica de tokens
 */
export class TokenEstimationService {
  
  /**
   * Estima tokens para generación basada en contextos y cantidad
   */
  static estimateForGeneration(
    contextos: string[],
    cantidad: number,
    estimationType: EstimationType,
    safetyMarginPercent: number = DEFAULT_SAFETY_MARGIN
  ): TokenEstimation {

    // Calcular tokens del contexto de entrada
    const contextTokens = contextos.reduce((total, contexto) => {
      return total + estimateTokens(contexto || '');
    }, 0);

    // Calcular tokens de generación basado en cantidad
    const baseTokensPerItem = BASE_TOKENS_PER_ITEM[estimationType];
    let generationTokens: number;

    // Para algunos tipos, la cantidad no afecta (mapas mentales, resúmenes, etc.)
    if (this.isQuantityIndependent(estimationType)) {
      generationTokens = baseTokensPerItem;
    } else {
      generationTokens = baseTokensPerItem * Math.max(1, cantidad);
    }

    // Calcular margen de seguridad
    const baseTotal = contextTokens + generationTokens;
    const safetyMargin = Math.ceil(baseTotal * safetyMarginPercent);

    // Total estimado
    const totalEstimated = baseTotal + safetyMargin;

    // Verificar límites máximos
    const maxAllowed = MAX_TOKENS_PER_REQUEST[estimationType];
    const isWithinLimits = totalEstimated <= maxAllowed;

    return {
      contextTokens,
      generationTokens,
      safetyMargin,
      totalEstimated: isWithinLimits ? totalEstimated : maxAllowed,
      isWithinLimits,
      maxAllowed
    };
  }

  /**
   * Estima tokens para generación basada en chunks
   */
  static estimateForChunks(
    chunks: Chunk[],
    cantidad: number,
    estimationType: EstimationType,
    safetyMarginPercent: number = DEFAULT_SAFETY_MARGIN
  ): TokenEstimation {

    // Calcular tokens del contexto de entrada (todos los chunks)
    const contextTokens = chunks.reduce((total, chunk) => {
      return total + estimateTokens(chunk.content || '');
    }, 0);

    // Calcular overhead por procesamiento de chunks
    const chunkingOverhead = this.calculateChunkingOverhead(chunks, estimationType);

    // Calcular tokens de generación basado en cantidad
    const baseTokensPerItem = BASE_TOKENS_PER_ITEM[estimationType];
    let generationTokens: number;

    // Para algunos tipos, la cantidad no afecta (mapas mentales, resúmenes, etc.)
    if (this.isQuantityIndependent(estimationType)) {
      generationTokens = baseTokensPerItem;
    } else {
      generationTokens = baseTokensPerItem * Math.max(1, cantidad);
    }

    // Calcular margen de seguridad
    const baseTotal = contextTokens + generationTokens + chunkingOverhead;
    const safetyMargin = Math.ceil(baseTotal * safetyMarginPercent);

    // Total estimado
    const totalEstimated = baseTotal + safetyMargin;

    // Verificar límites máximos
    const maxAllowed = MAX_TOKENS_PER_REQUEST[estimationType];
    const isWithinLimits = totalEstimated <= maxAllowed;

    return {
      contextTokens,
      generationTokens,
      safetyMargin,
      totalEstimated: isWithinLimits ? totalEstimated : maxAllowed,
      isWithinLimits,
      maxAllowed,
      chunkingOverhead,
      chunksCount: chunks.length
    };
  }

  /**
   * Calcula el overhead adicional por procesamiento de chunks
   */
  private static calculateChunkingOverhead(chunks: Chunk[], estimationType: EstimationType): number {
    const baseOverheadPerChunk = 100; // Tokens base por chunk para metadatos y contexto
    const combinationOverhead = chunks.length > 1 ? 500 : 0; // Overhead por combinar resultados

    // Overhead adicional según el tipo de estimación
    const typeMultiplier = {
      'TEST_GENERATION': 1.2,        // Tests requieren más overhead para distribución
      'FLASHCARD_GENERATION': 1.0,   // Flashcards son más simples
      'MIND_MAP_GENERATION': 1.5,    // Mapas mentales requieren más procesamiento
      'SUMMARY_GENERATION': 1.3,     // Resúmenes requieren combinación compleja
      'AI_CHAT': 1.0,               // Chat es directo
      'STUDY_PLANNING': 1.4         // Planificación requiere análisis complejo
    }[estimationType] || 1.0;

    return Math.ceil((baseOverheadPerChunk * chunks.length + combinationOverhead) * typeMultiplier);
  }
  
  /**
   * Estima tokens para funciones que no dependen de cantidad
   */
  static estimateForSingleGeneration(
    contextos: string[],
    estimationType: EstimationType,
    safetyMarginPercent: number = DEFAULT_SAFETY_MARGIN
  ): TokenEstimation {
    return this.estimateForGeneration(contextos, 1, estimationType, safetyMarginPercent);
  }
  
  /**
   * Obtiene estimación fija como fallback
   */
  static getFallbackEstimation(estimationType: EstimationType): number {
    return BASE_TOKENS_PER_ITEM[estimationType];
  }
  
  /**
   * Verifica si un tipo de estimación es independiente de la cantidad
   */
  private static isQuantityIndependent(estimationType: EstimationType): boolean {
    const quantityIndependentTypes: EstimationType[] = [
      'MIND_MAP_GENERATION',
      'SUMMARY_GENERATION',
      'AI_CHAT',
      'STUDY_PLANNING'
    ];
    
    return quantityIndependentTypes.includes(estimationType);
  }
  
  /**
   * Calcula estimación específica para tests
   */
  static estimateForTests(contextos: string[], cantidad: number): TokenEstimation {
    return this.estimateForGeneration(contextos, cantidad, 'TEST_GENERATION');
  }

  /**
   * Calcula estimación específica para tests con chunks
   */
  static estimateForTestsWithChunks(chunks: Chunk[], cantidad: number): TokenEstimation {
    return this.estimateForChunks(chunks, cantidad, 'TEST_GENERATION');
  }

  /**
   * Calcula estimación específica para flashcards
   */
  static estimateForFlashcards(contextos: string[], cantidad: number): TokenEstimation {
    return this.estimateForGeneration(contextos, cantidad, 'FLASHCARD_GENERATION');
  }

  /**
   * Calcula estimación específica para flashcards con chunks
   */
  static estimateForFlashcardsWithChunks(chunks: Chunk[], cantidad: number): TokenEstimation {
    return this.estimateForChunks(chunks, cantidad, 'FLASHCARD_GENERATION');
  }

  /**
   * Calcula estimación específica para mapas mentales
   */
  static estimateForMindMaps(contextos: string[]): TokenEstimation {
    return this.estimateForSingleGeneration(contextos, 'MIND_MAP_GENERATION');
  }

  /**
   * Calcula estimación específica para mapas mentales con chunks
   */
  static estimateForMindMapsWithChunks(chunks: Chunk[]): TokenEstimation {
    return this.estimateForChunks(chunks, 1, 'MIND_MAP_GENERATION');
  }

  /**
   * Calcula estimación específica para resúmenes
   */
  static estimateForSummaries(contextos: string[]): TokenEstimation {
    return this.estimateForSingleGeneration(contextos, 'SUMMARY_GENERATION');
  }

  /**
   * Calcula estimación específica para resúmenes con chunks
   */
  static estimateForSummariesWithChunks(chunks: Chunk[]): TokenEstimation {
    return this.estimateForChunks(chunks, 1, 'SUMMARY_GENERATION');
  }



  /**
   * Calcula estimación específica para chat
   */
  static estimateForChat(contextos: string[]): TokenEstimation {
    return this.estimateForSingleGeneration(contextos, 'AI_CHAT');
  }

  /**
   * Calcula estimación específica para chat con chunks
   */
  static estimateForChatWithChunks(chunks: Chunk[]): TokenEstimation {
    return this.estimateForChunks(chunks, 1, 'AI_CHAT');
  }
  
  /**
   * Calcula estimación específica para planificación (mantiene lógica fija)
   */
  static estimateForStudyPlanning(): TokenEstimation {
    const fixedTokens = BASE_TOKENS_PER_ITEM.STUDY_PLANNING;
    return {
      contextTokens: 0,
      generationTokens: fixedTokens,
      safetyMargin: 0,
      totalEstimated: fixedTokens,
      isWithinLimits: true,
      maxAllowed: MAX_TOKENS_PER_REQUEST.STUDY_PLANNING
    };
  }

  /**
   * Valida si una estimación está dentro de límites razonables
   */
  static validateEstimation(estimation: TokenEstimation, estimationType: EstimationType): {
    isValid: boolean;
    reason?: string;
    adjustedEstimation?: TokenEstimation;
  } {
    // Verificar límite máximo
    if (!estimation.isWithinLimits) {
      return {
        isValid: false,
        reason: `Estimación excede límite máximo de ${estimation.maxAllowed} tokens`,
        adjustedEstimation: {
          ...estimation,
          totalEstimated: estimation.maxAllowed,
          isWithinLimits: true
        }
      };
    }

    // Verificar que la estimación no sea demasiado pequeña (mínimo 100 tokens)
    if (estimation.totalEstimated < 100) {
      return {
        isValid: false,
        reason: 'Estimación demasiado pequeña, usando mínimo de 100 tokens',
        adjustedEstimation: {
          ...estimation,
          totalEstimated: 100,
          isWithinLimits: true
        }
      };
    }

    return { isValid: true };
  }

  /**
   * Función para logging de comparación estimación vs consumo real
   * Útil para ajustar algoritmos de estimación
   */
  static logEstimationAccuracy(
    estimationType: EstimationType,
    estimatedTokens: number,
    actualTokens: number,
    userId?: string
  ): void {
    const accuracy = actualTokens > 0 ? (estimatedTokens / actualTokens) : 0;
    const overestimation = estimatedTokens - actualTokens;
    const overestimationPercent = actualTokens > 0 ? (overestimation / actualTokens) * 100 : 0;

    console.log(`📊 [TOKEN_ESTIMATION] ${estimationType}:`, {
      estimated: estimatedTokens,
      actual: actualTokens,
      accuracy: `${(accuracy * 100).toFixed(1)}%`,
      overestimation: overestimation,
      overestimationPercent: `${overestimationPercent.toFixed(1)}%`,
      userId: userId || 'unknown'
    });

    // Alertar si hay sobregiros significativos (>50% de diferencia)
    if (Math.abs(overestimationPercent) > 50) {
      console.warn(`⚠️ [TOKEN_ESTIMATION] Estimación imprecisa para ${estimationType}: ${overestimationPercent.toFixed(1)}% de diferencia`);
    }
  }
}
